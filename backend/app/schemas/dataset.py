"""
数据集相关的Pydantic模式
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.models.dataset import DatasetStatus, GenerationMethod, AnnotationType


class DatasetBase(BaseModel):
    """数据集基础模式"""
    name: str = Field(..., min_length=1, max_length=255, description="数据集名称")
    description: Optional[str] = Field(None, description="数据集描述")
    targets: List[str] = Field(..., description="军事目标类型列表")
    weather_conditions: List[str] = Field(..., description="天气条件列表")
    terrain_scenes: List[str] = Field(..., description="地形场景列表")
    generation_method: GenerationMethod = Field(GenerationMethod.STABLE_DIFFUSION, description="生成方法")
    image_size: Dict[str, int] = Field({"width": 512, "height": 512}, description="图像尺寸")
    train_split: float = Field(0.8, ge=0.1, le=0.9, description="训练集比例")
    val_split: float = Field(0.1, ge=0.05, le=0.5, description="验证集比例")
    test_split: float = Field(0.1, ge=0.05, le=0.5, description="测试集比例")


class DatasetCreate(DatasetBase):
    """创建数据集模式"""
    total_images: int = Field(..., ge=1, le=10000, description="生成图像总数")


class DatasetUpdate(BaseModel):
    """更新数据集模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="数据集名称")
    description: Optional[str] = Field(None, description="数据集描述")
    status: Optional[DatasetStatus] = Field(None, description="数据集状态")


class DatasetResponse(DatasetBase):
    """数据集响应模式"""
    id: int
    status: DatasetStatus
    total_images: int
    generated_images: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class GenerationConfig(BaseModel):
    """图像生成配置"""
    targets: List[str]
    weather_conditions: List[str]
    terrain_scenes: List[str]
    generation_method: GenerationMethod = GenerationMethod.STABLE_DIFFUSION
    image_size: Dict[str, int] = {"width": 512, "height": 512}
    batch_size: int = Field(1, ge=1, le=10, description="批次大小")
    inference_steps: int = Field(50, ge=10, le=100, description="推理步数")
    guidance_scale: float = Field(7.5, ge=1.0, le=20.0, description="引导比例")
    seed: Optional[int] = Field(None, description="随机种子") 