"""
数据集管理服务
"""

import os
import shutil
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
from loguru import logger

from app.models.dataset import Dataset, Image, Annotation, DatasetStatus
from app.schemas.dataset import DatasetCreate, DatasetUpdate
from app.core.config import settings


class DatasetService:
    """数据集管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_datasets(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        status: Optional[str] = None
    ) -> List[Dataset]:
        """获取数据集列表"""
        query = self.db.query(Dataset)
        
        if status:
            query = query.filter(Dataset.status == status)
        
        return query.offset(skip).limit(limit).all()
    
    def get_dataset(self, dataset_id: int) -> Optional[Dataset]:
        """获取单个数据集"""
        return self.db.query(Dataset).filter(Dataset.id == dataset_id).first()
    
    def get_dataset_by_name(self, name: str) -> Optional[Dataset]:
        """根据名称获取数据集"""
        return self.db.query(Dataset).filter(Dataset.name == name).first()
    
    def create_dataset(self, dataset_data: DatasetCreate) -> Dataset:
        """创建数据集"""
        # 创建数据集记录
        dataset = Dataset(
            name=dataset_data.name,
            description=dataset_data.description,
            total_images=dataset_data.total_images,
            targets=dataset_data.targets,
            weather_conditions=dataset_data.weather_conditions,
            terrain_scenes=dataset_data.terrain_scenes,
            generation_method=dataset_data.generation_method,
            image_size=dataset_data.image_size,
            train_split=dataset_data.train_split,
            val_split=dataset_data.val_split,
            test_split=dataset_data.test_split,
            status=DatasetStatus.ACTIVE
        )
        
        self.db.add(dataset)
        self.db.commit()
        self.db.refresh(dataset)
        
        # 创建数据集目录
        self._create_dataset_directories(dataset.id)
        
        logger.info(f"创建数据集: {dataset.name} (ID: {dataset.id})")
        return dataset
    
    def update_dataset(self, dataset_id: int, dataset_data: DatasetUpdate) -> Dataset:
        """更新数据集"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError("数据集不存在")
        
        # 更新字段
        update_data = dataset_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(dataset, field, value)
        
        self.db.commit()
        self.db.refresh(dataset)
        
        logger.info(f"更新数据集: {dataset.name} (ID: {dataset.id})")
        return dataset
    
    def update_dataset_status(self, dataset_id: int, status: str) -> Dataset:
        """更新数据集状态"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError("数据集不存在")
        
        dataset.status = status
        self.db.commit()
        self.db.refresh(dataset)
        
        return dataset
    
    def delete_dataset(self, dataset_id: int) -> bool:
        """删除数据集"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            return False
        
        # 删除文件系统中的数据集目录
        dataset_dir = os.path.join(settings.DATASET_DIR, str(dataset_id))
        if os.path.exists(dataset_dir):
            shutil.rmtree(dataset_dir)
        
        # 删除数据库记录（级联删除相关的图像和标注）
        self.db.delete(dataset)
        self.db.commit()
        
        logger.info(f"删除数据集: {dataset.name} (ID: {dataset_id})")
        return True
    
    def get_dataset_stats(self, dataset_id: int) -> Dict[str, Any]:
        """获取数据集统计信息"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError("数据集不存在")
        
        # 基础统计
        total_images = self.db.query(func.count(Image.id)).filter(
            Image.dataset_id == dataset_id
        ).scalar()
        
        # 按分割类型统计
        split_stats = self.db.query(
            Image.split_type,
            func.count(Image.id)
        ).filter(
            Image.dataset_id == dataset_id
        ).group_by(Image.split_type).all()
        
        # 按目标类型统计
        target_stats = self.db.query(
            Image.target_type,
            func.count(Image.id)
        ).filter(
            Image.dataset_id == dataset_id
        ).group_by(Image.target_type).all()
        
        # 按天气条件统计
        weather_stats = self.db.query(
            Image.weather_condition,
            func.count(Image.id)
        ).filter(
            Image.dataset_id == dataset_id
        ).group_by(Image.weather_condition).all()
        
        # 按地形场景统计
        terrain_stats = self.db.query(
            Image.terrain_scene,
            func.count(Image.id)
        ).filter(
            Image.dataset_id == dataset_id
        ).group_by(Image.terrain_scene).all()
        
        # 标注统计
        annotation_count = self.db.query(func.count(Annotation.id)).filter(
            Annotation.dataset_id == dataset_id
        ).scalar()
        
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "total_images": total_images,
            "generated_images": dataset.generated_images,
            "target_images": dataset.total_images,
            "completion_rate": (dataset.generated_images / dataset.total_images * 100) if dataset.total_images > 0 else 0,
            "split_distribution": {split: count for split, count in split_stats},
            "target_distribution": {target: count for target, count in target_stats},
            "weather_distribution": {weather: count for weather, count in weather_stats},
            "terrain_distribution": {terrain: count for terrain, count in terrain_stats},
            "annotation_count": annotation_count,
            "status": dataset.status
        }
    
    def increment_generated_count(self, dataset_id: int) -> None:
        """增加已生成图像计数"""
        dataset = self.get_dataset(dataset_id)
        if dataset:
            dataset.generated_images += 1
            
            # 检查是否完成
            if dataset.generated_images >= dataset.total_images:
                dataset.status = DatasetStatus.COMPLETED
            
            self.db.commit()
    
    def _create_dataset_directories(self, dataset_id: int) -> None:
        """创建数据集目录结构"""
        base_dir = os.path.join(settings.DATASET_DIR, str(dataset_id))
        
        # 创建主目录和子目录
        directories = [
            base_dir,
            os.path.join(base_dir, "images"),
            os.path.join(base_dir, "annotations"),
            os.path.join(base_dir, "train"),
            os.path.join(base_dir, "val"),
            os.path.join(base_dir, "test")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info(f"创建数据集目录: {base_dir}")
    
    def get_dataset_path(self, dataset_id: int) -> str:
        """获取数据集路径"""
        return os.path.join(settings.DATASET_DIR, str(dataset_id))
    
    def get_images_path(self, dataset_id: int) -> str:
        """获取图像目录路径"""
        return os.path.join(self.get_dataset_path(dataset_id), "images")
    
    def get_annotations_path(self, dataset_id: int) -> str:
        """获取标注目录路径"""
        return os.path.join(self.get_dataset_path(dataset_id), "annotations") 