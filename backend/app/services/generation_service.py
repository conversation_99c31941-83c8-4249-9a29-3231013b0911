"""
图像生成服务
"""

import os
import time
import random
from typing import Dict, Any, List, Tuple, Optional
from PIL import Image as PILImage
import numpy as np
from loguru import logger
from sqlalchemy.orm import Session

from app.models.dataset import Dataset, Image, Annotation, GenerationMethod, AnnotationType, GenerationTask
from app.schemas.dataset import GenerationConfig
from app.services.dataset_service import DatasetService
from app.core.config import settings


class GenerationService:
    """图像生成服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.dataset_service = DatasetService(db)
        self._generation_tasks = {}  # 存储生成任务状态
    
    async def generate_dataset_images(self, dataset_id: int) -> None:
        """生成数据集图像（后台任务）"""
        try:
            dataset = self.dataset_service.get_dataset(dataset_id)
            if not dataset:
                logger.error(f"数据集不存在: {dataset_id}")
                return
            
            logger.info(f"开始生成数据集图像: {dataset.name}")
            
            # 初始化任务状态
            self._generation_tasks[dataset_id] = {
                "status": "running",
                "progress": 0,
                "current_image": 0,
                "total_images": dataset.total_images,
                "start_time": time.time()
            }
            
            # 生成图像组合
            combinations = self._generate_combinations(dataset)
            
            # 生成图像
            for i, (target, weather, terrain) in enumerate(combinations):
                if self._generation_tasks.get(dataset_id, {}).get("status") == "stopped":
                    break
                
                try:
                    # 生成单张图像
                    image_result = self._generate_single_image(
                        dataset_id, target, weather, terrain, i
                    )
                    
                    if image_result:
                        # 保存图像记录到数据库
                        self._save_image_record(dataset_id, image_result)
                        
                        # 生成标注
                        self._generate_annotations(image_result["image_id"])
                        
                        # 更新进度
                        self.dataset_service.increment_generated_count(dataset_id)
                        self._update_task_progress(dataset_id, i + 1)
                    
                except Exception as e:
                    logger.error(f"生成图像失败: {e}")
                    continue
            
            # 完成任务
            self._complete_generation_task(dataset_id)
            logger.info(f"数据集图像生成完成: {dataset.name}")
            
        except Exception as e:
            logger.error(f"生成数据集图像失败: {e}")
            self._fail_generation_task(dataset_id, str(e))
    
    def generate_test_image(self, config: GenerationConfig) -> Dict[str, Any]:
        """生成测试图像"""
        start_time = time.time()
        
        # 选择第一个组合进行测试
        target = config.targets[0]
        weather = config.weather_conditions[0]
        terrain = config.terrain_scenes[0]
        
        # 生成图像
        if config.generation_method == GenerationMethod.STABLE_DIFFUSION:
            image_path = self._generate_with_stable_diffusion(
                target, weather, terrain, config
            )
        elif config.generation_method == GenerationMethod.TRADITIONAL:
            image_path = self._generate_with_traditional_method(
                target, weather, terrain, config
            )
        else:
            raise ValueError(f"不支持的生成方法: {config.generation_method}")
        
        generation_time = time.time() - start_time
        
        return {
            "image_path": image_path,
            "generation_time": generation_time,
            "target": target,
            "weather": weather,
            "terrain": terrain
        }
    
    def get_generation_status(self, dataset_id: int) -> Dict[str, Any]:
        """获取生成任务状态"""
        task = self._generation_tasks.get(dataset_id, {})
        return {
            "task_status": task.get("status", "unknown"),
            "progress": task.get("progress", 0),
            "current_image": task.get("current_image", 0),
            "start_time": task.get("start_time"),
            "estimated_remaining": self._estimate_remaining_time(dataset_id)
        }
    
    def stop_generation(self, dataset_id: int) -> None:
        """停止生成任务"""
        if dataset_id in self._generation_tasks:
            self._generation_tasks[dataset_id]["status"] = "stopped"
            logger.info(f"停止生成任务: {dataset_id}")
    
    def _generate_combinations(self, dataset: Dataset) -> List[Tuple[str, str, str]]:
        """生成目标、天气、地形的组合"""
        # 计算每种组合需要生成的图像数量
        total_combinations = len(dataset.targets) * len(dataset.weather_conditions) * len(dataset.terrain_scenes)
        images_per_combination = max(1, dataset.total_images // total_combinations)
        
        combinations = []
        for target in dataset.targets:
            for weather in dataset.weather_conditions:
                for terrain in dataset.terrain_scenes:
                    for _ in range(images_per_combination):
                        combinations.append((target, weather, terrain))
        
        # 如果还需要更多图像，随机补充
        while len(combinations) < dataset.total_images:
            target = random.choice(dataset.targets)
            weather = random.choice(dataset.weather_conditions)
            terrain = random.choice(dataset.terrain_scenes)
            combinations.append((target, weather, terrain))
        
        # 随机打乱顺序
        random.shuffle(combinations)
        return combinations[:dataset.total_images]
    
    def _generate_single_image(
        self, 
        dataset_id: int, 
        target: str, 
        weather: str, 
        terrain: str, 
        index: int
    ) -> Dict[str, Any]:
        """生成单张图像"""
        dataset = self.dataset_service.get_dataset(dataset_id)
        if not dataset:
            return None
        
        # 生成文件名
        filename = f"{target}_{weather}_{terrain}_{index:06d}.jpg"
        images_dir = self.dataset_service.get_images_path(dataset_id)
        image_path = os.path.join(images_dir, filename)
        
        try:
            # 根据生成方法生成图像
            if dataset.generation_method == GenerationMethod.STABLE_DIFFUSION:
                self._generate_with_stable_diffusion_simple(
                    target, weather, terrain, image_path, dataset.image_size
                )
            elif dataset.generation_method == GenerationMethod.TRADITIONAL:
                self._generate_with_traditional_method_simple(
                    target, weather, terrain, image_path, dataset.image_size
                )
            else:
                logger.warning(f"不支持的生成方法: {dataset.generation_method}")
                return None
            
            # 确定数据集分割
            split_type = self._determine_split_type(dataset, index)
            
            return {
                "filename": filename,
                "file_path": image_path,
                "target_type": target,
                "weather_condition": weather,
                "terrain_scene": terrain,
                "generation_method": dataset.generation_method,
                "split_type": split_type,
                "dataset_id": dataset_id
            }
            
        except Exception as e:
            logger.error(f"生成图像失败: {e}")
            return None
    
    def _generate_with_stable_diffusion_simple(
        self, 
        target: str, 
        weather: str, 
        terrain: str, 
        output_path: str,
        image_size: Dict[str, int]
    ) -> None:
        """使用Stable Diffusion生成图像（简化版）"""
        # 这里是简化的实现，实际应该使用真正的Stable Diffusion模型
        # 目前生成一个占位图像
        
        width = image_size.get("width", 512)
        height = image_size.get("height", 512)
        
        # 创建一个简单的占位图像
        image = PILImage.new("RGB", (width, height), color=self._get_color_for_combination(target, weather, terrain))
        
        # 添加一些简单的图案
        pixels = np.array(image)
        
        # 根据目标类型添加不同的图案
        if target == "tank":
            # 添加矩形图案
            pixels[height//3:2*height//3, width//3:2*width//3] = [100, 100, 100]
        elif target == "aircraft":
            # 添加三角形图案
            for i in range(height//4, 3*height//4):
                for j in range(width//2 - (i - height//4), width//2 + (i - height//4)):
                    if 0 <= j < width:
                        pixels[i, j] = [150, 150, 150]
        
        image = PILImage.fromarray(pixels)
        image.save(output_path, "JPEG", quality=95)
        
        logger.info(f"生成图像: {output_path}")
    
    def _generate_with_traditional_method_simple(
        self, 
        target: str, 
        weather: str, 
        terrain: str, 
        output_path: str,
        image_size: Dict[str, int]
    ) -> None:
        """使用传统方法生成图像（简化版）"""
        width = image_size.get("width", 512)
        height = image_size.get("height", 512)
        
        # 创建基础背景
        base_color = self._get_terrain_color(terrain)
        image = PILImage.new("RGB", (width, height), color=base_color)
        pixels = np.array(image)
        
        # 添加天气效果
        pixels = self._apply_weather_effect(pixels, weather)
        
        # 添加目标物体
        pixels = self._add_target_object(pixels, target)
        
        image = PILImage.fromarray(pixels)
        image.save(output_path, "JPEG", quality=95)
        
        logger.info(f"生成图像: {output_path}")
    
    def _get_color_for_combination(self, target: str, weather: str, terrain: str) -> Tuple[int, int, int]:
        """根据组合获取颜色"""
        # 基础颜色根据目标类型
        colors = {
            "tank": (100, 120, 80),
            "aircraft": (135, 206, 235),
            "ship": (70, 130, 180),
            "vehicle": (139, 69, 19),
            "helicopter": (176, 196, 222),
            "submarine": (25, 25, 112)
        }

        base_color = colors.get(target, (128, 128, 128))

        # 根据天气和地形调整颜色
        if weather == "night":
            base_color = tuple(int(c * 0.3) for c in base_color)
        elif weather == "foggy":
            base_color = tuple(int(c * 0.7 + 50) for c in base_color)

        if terrain == "urban":
            base_color = tuple(min(255, int(c * 1.1)) for c in base_color)

        return base_color
    
    def _get_terrain_color(self, terrain: str) -> Tuple[int, int, int]:
        """获取地形颜色"""
        colors = {
            "urban": (169, 169, 169),
            "island": (238, 203, 173),
            "rural": (107, 142, 35),
            "desert": (238, 203, 173),
            "forest": (34, 139, 34),
            "mountain": (139, 137, 137)
        }
        return colors.get(terrain, (128, 128, 128))
    
    def _apply_weather_effect(self, pixels: np.ndarray, weather: str) -> np.ndarray:
        """应用天气效果"""
        if weather == "rainy":
            # 添加雨滴效果（降低亮度）
            pixels = (pixels * 0.7).astype(np.uint8)
        elif weather == "snowy":
            # 添加雪花效果（增加白色噪点）
            noise = np.random.random(pixels.shape) > 0.95
            pixels[noise] = [255, 255, 255]
        elif weather == "foggy":
            # 添加雾气效果（增加灰度）
            pixels = (pixels * 0.8 + 50).astype(np.uint8)
        elif weather == "night":
            # 夜间效果（大幅降低亮度）
            pixels = (pixels * 0.3).astype(np.uint8)
        
        return pixels
    
    def _add_target_object(self, pixels: np.ndarray, target: str) -> np.ndarray:
        """添加目标物体"""
        height, width = pixels.shape[:2]
        
        if target == "tank":
            # 添加坦克形状
            pixels[height//2-20:height//2+20, width//2-30:width//2+30] = [60, 60, 60]
        elif target == "aircraft":
            # 添加飞机形状
            pixels[height//2-10:height//2+10, width//2-40:width//2+40] = [200, 200, 200]
        elif target == "ship":
            # 添加船只形状
            pixels[height//2-15:height//2+15, width//2-50:width//2+50] = [100, 100, 100]
        
        return pixels
    
    def _determine_split_type(self, dataset: Dataset, index: int) -> str:
        """确定数据集分割类型"""
        train_count = int(dataset.total_images * dataset.train_split)
        val_count = int(dataset.total_images * dataset.val_split)
        
        if index < train_count:
            return "train"
        elif index < train_count + val_count:
            return "val"
        else:
            return "test"
    
    def _save_image_record(self, dataset_id: int, image_data: Dict[str, Any]) -> int:
        """保存图像记录到数据库"""
        # 获取图像文件信息
        file_path = image_data["file_path"]
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        # 获取图像尺寸
        try:
            with PILImage.open(file_path) as img:
                width, height = img.size
                format_name = img.format
        except:
            width = height = 0
            format_name = "JPEG"
        
        # 创建图像记录
        image = Image(
            filename=image_data["filename"],
            file_path=file_path,
            file_size=file_size,
            width=width,
            height=height,
            format=format_name,
            target_type=image_data["target_type"],
            weather_condition=image_data["weather_condition"],
            terrain_scene=image_data["terrain_scene"],
            generation_method=image_data["generation_method"],
            split_type=image_data["split_type"],
            dataset_id=dataset_id
        )
        
        self.db.add(image)
        self.db.commit()
        self.db.refresh(image)
        
        return image.id
    
    def _generate_annotations(self, image_id: int) -> None:
        """生成图像标注"""
        image = self.db.query(Image).filter(Image.id == image_id).first()
        if not image:
            return
        
        # 生成分类标注
        classification_annotation = Annotation(
            annotation_type=AnnotationType.CLASSIFICATION,
            class_label=image.target_type,
            confidence=0.95,
            image_id=image_id,
            dataset_id=image.dataset_id
        )
        
        # 生成检测标注（边界框）
        detection_annotation = Annotation(
            annotation_type=AnnotationType.DETECTION,
            class_label=image.target_type,
            confidence=0.90,
            bbox_x=0.25,  # 相对坐标
            bbox_y=0.25,
            bbox_width=0.5,
            bbox_height=0.5,
            image_id=image_id,
            dataset_id=image.dataset_id
        )
        
        self.db.add_all([classification_annotation, detection_annotation])
        self.db.commit()
    
    def _update_task_progress(self, dataset_id: int, current_image: int) -> None:
        """更新任务进度"""
        if dataset_id in self._generation_tasks:
            task = self._generation_tasks[dataset_id]
            task["current_image"] = current_image
            task["progress"] = (current_image / task["total_images"]) * 100
    
    def _complete_generation_task(self, dataset_id: int) -> None:
        """完成生成任务"""
        if dataset_id in self._generation_tasks:
            self._generation_tasks[dataset_id]["status"] = "completed"
            self._generation_tasks[dataset_id]["progress"] = 100
    
    def _fail_generation_task(self, dataset_id: int, error_message: str) -> None:
        """任务失败"""
        if dataset_id in self._generation_tasks:
            self._generation_tasks[dataset_id]["status"] = "failed"
            self._generation_tasks[dataset_id]["error"] = error_message
    
    def _estimate_remaining_time(self, dataset_id: int) -> float:
        """估算剩余时间"""
        task = self._generation_tasks.get(dataset_id, {})
        if not task or task.get("status") != "running":
            return 0
        
        current_image = task.get("current_image", 0)
        total_images = task.get("total_images", 1)
        start_time = task.get("start_time", time.time())
        
        if current_image == 0:
            return 0
        
        elapsed_time = time.time() - start_time
        time_per_image = elapsed_time / current_image
        remaining_images = total_images - current_image
        
        return remaining_images * time_per_image

    # 新增的API方法
    def create_batch_generation_task(
        self,
        dataset_id: int,
        total_images: int,
        config: GenerationConfig
    ) -> int:
        """创建批量生成任务"""
        # 创建生成任务记录
        task = GenerationTask(
            dataset_id=dataset_id,
            total_images=total_images,
            status="pending",
            generation_params=config.model_dump()
        )

        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)

        return task.id

    def execute_batch_generation(self, task_id: int) -> None:
        """执行批量生成任务"""
        task = self.db.query(GenerationTask).filter(GenerationTask.id == task_id).first()
        if not task:
            logger.error(f"生成任务不存在: {task_id}")
            return

        try:
            task.status = "running"
            self.db.commit()

            # 执行生成
            self.generate_dataset_images(task.dataset_id)

            task.status = "completed"
            self.db.commit()

        except Exception as e:
            logger.error(f"批量生成任务失败: {e}")
            task.status = "failed"
            task.error_message = str(e)
            self.db.commit()

    def generate_single_image(self, config: GenerationConfig) -> Dict[str, Any]:
        """生成单张图像"""
        try:
            # 创建临时目录
            temp_dir = os.path.join(settings.STATIC_DIR, "temp_images")
            os.makedirs(temp_dir, exist_ok=True)

            # 生成文件名
            filename = f"single_{int(time.time())}_{random.randint(1000, 9999)}.jpg"
            image_path = os.path.join(temp_dir, filename)

            # 生成图像
            if config.generation_method == "stable_diffusion":
                self._generate_with_stable_diffusion_simple(
                    config.targets[0],
                    config.weather_conditions[0],
                    config.terrain_scenes[0],
                    image_path,
                    config.image_size
                )
            else:
                self._generate_with_traditional_method_simple(
                    config.targets[0],
                    config.weather_conditions[0],
                    config.terrain_scenes[0],
                    image_path,
                    config.image_size
                )

            # 生成访问URL
            image_url = f"/static/temp_images/{filename}"

            return {
                "image_path": image_path,
                "image_url": image_url,
                "generation_time": 1.0  # 简化的生成时间
            }

        except Exception as e:
            logger.error(f"单张图像生成失败: {e}")
            raise e

    def get_generation_tasks(
        self,
        skip: int = 0,
        limit: int = 100,
        status: str = None,
        dataset_id: int = None
    ) -> List[Dict[str, Any]]:
        """获取生成任务列表"""
        query = self.db.query(GenerationTask)

        if status:
            query = query.filter(GenerationTask.status == status)

        if dataset_id:
            query = query.filter(GenerationTask.dataset_id == dataset_id)

        tasks = query.offset(skip).limit(limit).all()

        return [
            {
                "id": task.id,
                "dataset_id": task.dataset_id,
                "total_images": task.total_images,
                "status": task.status,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "generation_params": task.generation_params,
                "error_message": task.error_message
            }
            for task in tasks
        ]

    def get_generation_task(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取生成任务详情"""
        task = self.db.query(GenerationTask).filter(GenerationTask.id == task_id).first()
        if not task:
            return None

        return {
            "id": task.id,
            "dataset_id": task.dataset_id,
            "total_images": task.total_images,
            "status": task.status,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "generation_params": task.generation_params,
            "error_message": task.error_message
        }

    def cancel_generation_task(self, task_id: int) -> bool:
        """取消生成任务"""
        task = self.db.query(GenerationTask).filter(GenerationTask.id == task_id).first()
        if not task:
            return False

        if task.status in ["pending", "running"]:
            task.status = "cancelled"
            self.db.commit()

            # 如果是运行中的任务，也要停止内存中的任务
            if task.dataset_id in self._generation_tasks:
                self._generation_tasks[task.dataset_id]["status"] = "stopped"

            return True

        return False