"""
图像生成API端点
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from loguru import logger

from app.core.database import get_db
from app.schemas.dataset import GenerationConfig
from app.services.generation_service import GenerationService
from app.services.dataset_service import DatasetService

router = APIRouter()


@router.post("/start/{dataset_id}")
async def start_generation(
    dataset_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """开始生成数据集图像"""
    try:
        # 检查数据集是否存在
        dataset_service = DatasetService(db)
        dataset = dataset_service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        # 检查数据集状态
        if dataset.status == "creating":
            raise HTTPException(status_code=400, detail="数据集正在生成中")
        
        if dataset.status == "completed":
            raise HTTPException(status_code=400, detail="数据集已完成生成")
        
        # 创建生成服务
        generation_service = GenerationService(db)
        
        # 启动后台生成任务
        background_tasks.add_task(
            generation_service.generate_dataset_images,
            dataset_id
        )
        
        # 更新数据集状态
        dataset_service.update_dataset_status(dataset_id, "creating")
        
        logger.info(f"开始生成数据集图像: {dataset.name}")
        return {
            "message": "图像生成任务已启动",
            "dataset_id": dataset_id,
            "status": "creating"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动图像生成失败: {e}")
        raise HTTPException(status_code=500, detail="启动图像生成失败")


@router.get("/status/{dataset_id}")
async def get_generation_status(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """获取生成任务状态"""
    try:
        dataset_service = DatasetService(db)
        dataset = dataset_service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        generation_service = GenerationService(db)
        status = generation_service.get_generation_status(dataset_id)
        
        return {
            "dataset_id": dataset_id,
            "status": dataset.status,
            "total_images": dataset.total_images,
            "generated_images": dataset.generated_images,
            "progress": (dataset.generated_images / dataset.total_images * 100) if dataset.total_images > 0 else 0,
            **status
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取生成状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取生成状态失败")


@router.post("/stop/{dataset_id}")
async def stop_generation(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """停止生成任务"""
    try:
        dataset_service = DatasetService(db)
        dataset = dataset_service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        if dataset.status != "creating":
            raise HTTPException(status_code=400, detail="没有正在进行的生成任务")
        
        generation_service = GenerationService(db)
        generation_service.stop_generation(dataset_id)
        
        # 更新数据集状态
        dataset_service.update_dataset_status(dataset_id, "active")
        
        logger.info(f"停止生成任务: {dataset.name}")
        return {
            "message": "生成任务已停止",
            "dataset_id": dataset_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止生成任务失败: {e}")
        raise HTTPException(status_code=500, detail="停止生成任务失败")


@router.post("/test")
async def test_generation(
    config: GenerationConfig,
    db: Session = Depends(get_db)
):
    """测试图像生成（生成单张图像）"""
    try:
        generation_service = GenerationService(db)
        
        # 生成测试图像
        result = generation_service.generate_test_image(config)
        
        return {
            "message": "测试图像生成成功",
            "image_path": result["image_path"],
            "generation_time": result["generation_time"],
            "config": config.model_dump()
        }
    except Exception as e:
        logger.error(f"测试图像生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试图像生成失败: {str(e)}")


@router.get("/methods")
async def get_generation_methods():
    """获取可用的生成方法"""
    return {
        "methods": [
            {
                "id": "traditional",
                "name": "传统代码合成",
                "description": "基于规则的图像合成方法",
                "supported": True
            },
            {
                "id": "stable_diffusion",
                "name": "Stable Diffusion",
                "description": "基于扩散模型的图像生成",
                "supported": True
            },
            {
                "id": "custom_model",
                "name": "定制训练模型",
                "description": "专门训练的军事目标生成模型",
                "supported": False  # 需要模型文件
            }
        ]
    }


@router.get("/prompts/examples")
async def get_prompt_examples():
    """获取提示词示例"""
    return {
        "examples": [
            {
                "target": "tank",
                "weather": "sunny",
                "terrain": "urban",
                "prompt": "A modern military tank in an urban environment, sunny day, high quality, realistic"
            },
            {
                "target": "aircraft",
                "weather": "cloudy",
                "terrain": "desert",
                "prompt": "A fighter jet flying over desert landscape, cloudy sky, military aircraft, detailed"
            },
            {
                "target": "ship",
                "weather": "foggy",
                "terrain": "island",
                "prompt": "A naval warship near an island, foggy weather, ocean scene, military vessel"
            }
        ]
    }


@router.get("/config/options")
async def get_generation_options():
    """获取生成配置选项"""
    return {
        "targets": [
            {"id": "tank", "name": "坦克", "description": "各类主战坦克、装甲车辆"},
            {"id": "aircraft", "name": "战机", "description": "战斗机、攻击机、轰炸机等军用飞机"},
            {"id": "warship", "name": "舰艇", "description": "驱逐舰、护卫舰、航母等军用舰艇"}
        ],
        "weather_conditions": [
            {"id": "sunny", "name": "晴天", "description": "晴朗天气"},
            {"id": "rainy", "name": "雨天", "description": "降雨天气"},
            {"id": "snowy", "name": "雪天", "description": "降雪天气"},
            {"id": "foggy", "name": "大雾", "description": "大雾天气"},
            {"id": "night", "name": "夜间", "description": "夜间场景"}
        ],
        "terrain_scenes": [
            {"id": "urban", "name": "城市", "description": "城市环境"},
            {"id": "island", "name": "岛屿", "description": "岛屿环境"},
            {"id": "rural", "name": "乡村", "description": "乡村环境"}
        ],
        "generation_methods": [
            {"id": "traditional", "name": "传统合成", "description": "基于模板的图像合成"},
            {"id": "stable_diffusion", "name": "AI生成", "description": "基于Stable Diffusion的AI生成"}
        ],
        "image_sizes": [
            {"width": 512, "height": 512, "name": "标准 (512x512)"},
            {"width": 768, "height": 768, "name": "高清 (768x768)"},
            {"width": 1024, "height": 1024, "name": "超高清 (1024x1024)"}
        ]
    }


@router.post("/batch")
async def batch_generation(
    dataset_id: int,
    total_images: int,
    generation_config: GenerationConfig,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """批量生成图像"""
    try:
        # 验证参数
        if total_images > 10000:
            raise HTTPException(status_code=400, detail="单次生成数量不能超过10000张")

        if total_images <= 0:
            raise HTTPException(status_code=400, detail="生成数量必须大于0")

        # 检查数据集是否存在
        dataset_service = DatasetService(db)
        dataset = dataset_service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")

        generation_service = GenerationService(db)

        # 创建批量生成任务
        task_id = generation_service.create_batch_generation_task(
            dataset_id=dataset_id,
            total_images=total_images,
            config=generation_config
        )

        # 在后台执行批量生成
        background_tasks.add_task(
            generation_service.execute_batch_generation,
            task_id
        )

        logger.info(f"创建批量生成任务: {task_id}, 数量: {total_images}")
        return {
            "message": "批量生成任务已创建",
            "task_id": task_id,
            "dataset_id": dataset_id,
            "total_images": total_images
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量生成失败: {str(e)}")


@router.post("/single")
async def single_generation(
    generation_config: GenerationConfig,
    db: Session = Depends(get_db)
):
    """单张图像生成"""
    try:
        generation_service = GenerationService(db)

        # 生成单张图像
        result = generation_service.generate_single_image(generation_config)

        return {
            "message": "单张图像生成成功",
            "image_path": result["image_path"],
            "image_url": result.get("image_url"),
            "generation_time": result["generation_time"],
            "config": generation_config.model_dump()
        }
    except Exception as e:
        logger.error(f"单张图像生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"单张图像生成失败: {str(e)}")


@router.get("/tasks")
async def get_generation_tasks(
    skip: int = 0,
    limit: int = 100,
    status: str = None,
    dataset_id: int = None,
    db: Session = Depends(get_db)
):
    """获取生成任务列表"""
    try:
        generation_service = GenerationService(db)
        tasks = generation_service.get_generation_tasks(
            skip=skip,
            limit=limit,
            status=status,
            dataset_id=dataset_id
        )
        return {
            "tasks": tasks,
            "total": len(tasks),
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        logger.error(f"获取生成任务列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取生成任务列表失败")


@router.get("/tasks/{task_id}")
async def get_generation_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """获取生成任务详情"""
    try:
        generation_service = GenerationService(db)
        task = generation_service.get_generation_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="生成任务不存在")
        return task
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取生成任务详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取生成任务详情失败")


@router.post("/tasks/{task_id}/cancel")
async def cancel_generation_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """取消生成任务"""
    try:
        generation_service = GenerationService(db)
        success = generation_service.cancel_generation_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="生成任务不存在或无法取消")

        return {
            "message": "生成任务已取消",
            "task_id": task_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消生成任务失败: {e}")
        raise HTTPException(status_code=500, detail="取消生成任务失败")