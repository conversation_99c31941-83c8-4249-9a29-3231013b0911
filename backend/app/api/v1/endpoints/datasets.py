"""
数据集管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile, Form
from sqlalchemy.orm import Session
from loguru import logger
import os
import time
import shutil
from pathlib import Path

from app.core.database import get_db
from app.schemas.dataset import DatasetCreate, DatasetUpdate, DatasetResponse, GenerationConfig
from app.services.dataset_service import DatasetService
from app.core.config import settings

router = APIRouter()


@router.get("/", response_model=List[DatasetResponse])
async def get_datasets(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[str] = Query(None, description="按状态筛选"),
    db: Session = Depends(get_db)
):
    """获取数据集列表"""
    try:
        service = DatasetService(db)
        datasets = service.get_datasets(skip=skip, limit=limit, status=status)
        return datasets
    except Exception as e:
        logger.error(f"获取数据集列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据集列表失败")


@router.get("/{dataset_id}", response_model=DatasetResponse)
async def get_dataset(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """获取单个数据集详情"""
    try:
        service = DatasetService(db)
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        return dataset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据集详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据集详情失败")


@router.post("/", response_model=DatasetResponse)
async def create_dataset(
    dataset: DatasetCreate,
    db: Session = Depends(get_db)
):
    """创建新数据集"""
    try:
        service = DatasetService(db)
        
        # 检查数据集名称是否已存在
        existing = service.get_dataset_by_name(dataset.name)
        if existing:
            raise HTTPException(status_code=400, detail="数据集名称已存在")
        
        # 验证配置参数
        _validate_dataset_config(dataset)
        
        new_dataset = service.create_dataset(dataset)
        logger.info(f"创建数据集成功: {new_dataset.name}")
        return new_dataset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建数据集失败: {e}")
        raise HTTPException(status_code=500, detail="创建数据集失败")


@router.put("/{dataset_id}", response_model=DatasetResponse)
async def update_dataset(
    dataset_id: int,
    dataset_update: DatasetUpdate,
    db: Session = Depends(get_db)
):
    """更新数据集"""
    try:
        service = DatasetService(db)
        
        # 检查数据集是否存在
        existing = service.get_dataset(dataset_id)
        if not existing:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        # 如果更新名称，检查是否重复
        if dataset_update.name and dataset_update.name != existing.name:
            name_exists = service.get_dataset_by_name(dataset_update.name)
            if name_exists:
                raise HTTPException(status_code=400, detail="数据集名称已存在")
        
        updated_dataset = service.update_dataset(dataset_id, dataset_update)
        logger.info(f"更新数据集成功: {updated_dataset.name}")
        return updated_dataset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新数据集失败: {e}")
        raise HTTPException(status_code=500, detail="更新数据集失败")


@router.delete("/{dataset_id}")
async def delete_dataset(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """删除数据集"""
    try:
        service = DatasetService(db)
        
        # 检查数据集是否存在
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        # 检查是否有正在进行的生成任务
        if dataset.status == "creating":
            raise HTTPException(status_code=400, detail="数据集正在生成中，无法删除")
        
        service.delete_dataset(dataset_id)
        logger.info(f"删除数据集成功: {dataset.name}")
        return {"message": "数据集删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除数据集失败: {e}")
        raise HTTPException(status_code=500, detail="删除数据集失败")


@router.get("/{dataset_id}/stats")
async def get_dataset_stats(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """获取数据集统计信息"""
    try:
        service = DatasetService(db)
        
        # 检查数据集是否存在
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")
        
        stats = service.get_dataset_stats(dataset_id)
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据集统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据集统计信息失败")


@router.get("/config/options")
async def get_config_options():
    """获取配置选项"""
    return {
        "military_targets": settings.MILITARY_TARGETS,
        "weather_conditions": settings.WEATHER_CONDITIONS,
        "terrain_scenes": settings.TERRAIN_SCENES,
        "generation_methods": ["traditional", "stable_diffusion", "custom_model"],
        "image_sizes": [
            {"width": 256, "height": 256},
            {"width": 512, "height": 512},
            {"width": 768, "height": 768},
            {"width": 1024, "height": 1024}
        ]
    }


def _validate_dataset_config(dataset: DatasetCreate):
    """验证数据集配置"""
    # 验证军事目标类型
    invalid_targets = set(dataset.targets) - set(settings.MILITARY_TARGETS)
    if invalid_targets:
        raise HTTPException(
            status_code=400, 
            detail=f"无效的军事目标类型: {list(invalid_targets)}"
        )
    
    # 验证天气条件
    invalid_weather = set(dataset.weather_conditions) - set(settings.WEATHER_CONDITIONS)
    if invalid_weather:
        raise HTTPException(
            status_code=400, 
            detail=f"无效的天气条件: {list(invalid_weather)}"
        )
    
    # 验证地形场景
    invalid_terrain = set(dataset.terrain_scenes) - set(settings.TERRAIN_SCENES)
    if invalid_terrain:
        raise HTTPException(
            status_code=400, 
            detail=f"无效的地形场景: {list(invalid_terrain)}"
        )
    
    # 验证数据集分割比例
    total_split = dataset.train_split + dataset.val_split + dataset.test_split
    if abs(total_split - 1.0) > 0.01:
        raise HTTPException(
            status_code=400,
            detail="训练集、验证集和测试集比例之和必须等于1.0"
        )


@router.post("/{dataset_id}/images/upload")
async def upload_images(
    dataset_id: int,
    files: List[UploadFile] = File(...),
    metadata: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """批量上传图像到数据集"""
    try:
        service = DatasetService(db)

        # 检查数据集是否存在
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")

        # 验证文件类型
        allowed_types = {"image/jpeg", "image/png", "image/jpg"}
        uploaded_files = []

        for file in files:
            if file.content_type not in allowed_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件类型: {file.content_type}"
                )

            # 检查文件大小 (最大10MB)
            if file.size and file.size > 10 * 1024 * 1024:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件 {file.filename} 过大，最大支持10MB"
                )

        # 创建上传目录
        upload_dir = Path(settings.DATASET_DIR) / str(dataset_id) / "uploads"
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 保存文件
        for file in files:
            # 生成唯一文件名
            unique_filename = f"{int(time.time())}_{file.filename}"
            file_path = upload_dir / unique_filename

            # 保存文件
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # 记录上传的文件信息
            uploaded_files.append({
                "original_name": file.filename,
                "saved_name": unique_filename,
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "content_type": file.content_type
            })

        logger.info(f"成功上传 {len(uploaded_files)} 个文件到数据集 {dataset_id}")

        return {
            "message": f"成功上传 {len(uploaded_files)} 个文件",
            "dataset_id": dataset_id,
            "uploaded_files": uploaded_files
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.get("/{dataset_id}/images")
async def get_dataset_images(
    dataset_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    split_type: Optional[str] = Query(None, description="数据分割类型: train/val/test"),
    db: Session = Depends(get_db)
):
    """获取数据集图像列表"""
    try:
        service = DatasetService(db)

        # 检查数据集是否存在
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")

        images = service.get_dataset_images(
            dataset_id=dataset_id,
            skip=skip,
            limit=limit,
            split_type=split_type
        )

        return {
            "dataset_id": dataset_id,
            "images": images,
            "total": len(images),
            "skip": skip,
            "limit": limit
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据集图像列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取数据集图像列表失败")


@router.delete("/{dataset_id}/images/{image_id}")
async def delete_image(
    dataset_id: int,
    image_id: int,
    db: Session = Depends(get_db)
):
    """删除数据集中的图像"""
    try:
        service = DatasetService(db)

        # 检查数据集是否存在
        dataset = service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(status_code=404, detail="数据集不存在")

        # 删除图像
        success = service.delete_image(image_id)
        if not success:
            raise HTTPException(status_code=404, detail="图像不存在")

        return {"message": "图像删除成功", "image_id": image_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除图像失败: {e}")
        raise HTTPException(status_code=500, detail="删除图像失败")