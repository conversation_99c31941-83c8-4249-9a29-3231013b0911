# 数据库设计文档

## 概述

本文档详细描述了军事目标图像数据集生成系统的数据库设计。系统采用PostgreSQL作为主数据库，Redis作为缓存数据库，使用SQLAlchemy ORM和Alembic进行数据库管理。

## 数据库架构

### 技术栈
- **主数据库**: PostgreSQL 13+
- **缓存数据库**: Redis 6+
- **ORM框架**: SQLAlchemy
- **迁移工具**: Alembic
- **连接池**: SQLAlchemy连接池

### 设计原则
- **规范化设计**: 遵循第三范式，减少数据冗余
- **性能优化**: 关键字段建立索引，优化查询性能
- **数据完整性**: 使用外键约束确保数据一致性
- **可扩展性**: 模块化设计，便于功能扩展
- **审计追踪**: 记录数据变更历史和用户操作

## 数据模型详细设计

### 1. 用户管理模块

#### 1.1 users (用户表)
用户基本信息和权限管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 用户ID |
| username | String(50) | Unique, Not Null | 用户名 |
| email | String(255) | Unique, Not Null | 邮箱地址 |
| hashed_password | String(255) | Not Null | 加密密码 |
| full_name | String(100) | | 真实姓名 |
| role | String(20) | Default: viewer | 用户角色 |
| status | String(20) | Default: active | 用户状态 |
| is_active | Boolean | Default: True | 是否激活 |
| is_superuser | Boolean | Default: False | 是否超级用户 |
| avatar_url | String(500) | | 头像URL |
| bio | Text | | 个人简介 |
| created_at | DateTime | Auto | 创建时间 |
| updated_at | DateTime | Auto | 更新时间 |
| last_login_at | DateTime | | 最后登录时间 |

**索引**:
- `ix_users_username` (唯一)
- `ix_users_email` (唯一)

**枚举值**:
- role: admin, researcher, viewer
- status: active, inactive, suspended

#### 1.2 user_sessions (用户会话表)
用户登录会话管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 会话ID |
| user_id | Integer | FK(users.id), Not Null | 用户ID |
| session_token | String(255) | Unique, Not Null | 会话令牌 |
| ip_address | String(45) | | IP地址 |
| user_agent | Text | | 用户代理 |
| created_at | DateTime | Auto | 创建时间 |
| expires_at | DateTime | Not Null | 过期时间 |
| last_accessed_at | DateTime | Auto | 最后访问时间 |

**索引**:
- `ix_user_sessions_session_token` (唯一)

#### 1.3 user_activities (用户活动日志表)
记录用户操作历史

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 活动ID |
| user_id | Integer | FK(users.id), Not Null | 用户ID |
| action | String(100) | Not Null | 操作类型 |
| resource_type | String(50) | | 资源类型 |
| resource_id | Integer | | 资源ID |
| description | Text | | 操作描述 |
| ip_address | String(45) | | IP地址 |
| user_agent | Text | | 用户代理 |
| created_at | DateTime | Auto | 创建时间 |

### 2. 数据集管理模块

#### 2.1 datasets (数据集表)
数据集基本信息和配置

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 数据集ID |
| name | String(255) | Unique, Not Null | 数据集名称 |
| description | Text | | 数据集描述 |
| status | String(50) | Default: creating | 数据集状态 |
| total_images | Integer | Default: 0 | 总图像数 |
| generated_images | Integer | Default: 0 | 已生成图像数 |
| targets | JSON | | 军事目标类型列表 |
| weather_conditions | JSON | | 天气条件列表 |
| terrain_scenes | JSON | | 地形场景列表 |
| generation_method | String(50) | Default: stable_diffusion | 生成方法 |
| image_size | JSON | Default: {"width": 512, "height": 512} | 图像尺寸 |
| train_split | Float | Default: 0.8 | 训练集比例 |
| val_split | Float | Default: 0.1 | 验证集比例 |
| test_split | Float | Default: 0.1 | 测试集比例 |
| creator_id | Integer | FK(users.id) | 创建者ID |
| created_at | DateTime | Auto | 创建时间 |
| updated_at | DateTime | Auto | 更新时间 |

**索引**:
- `ix_datasets_name` (唯一)

**枚举值**:
- status: creating, active, completed, archived, error
- generation_method: traditional, stable_diffusion, custom_model

#### 2.2 images (图像表)
图像文件信息和属性

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 图像ID |
| filename | String(255) | Not Null | 文件名 |
| file_path | String(500) | Not Null | 文件路径 |
| file_size | Integer | | 文件大小(字节) |
| width | Integer | | 图像宽度 |
| height | Integer | | 图像高度 |
| format | String(10) | | 图像格式 |
| target_type | String(50) | | 军事目标类型 |
| weather_condition | String(50) | | 天气条件 |
| terrain_scene | String(50) | | 地形场景 |
| generation_method | String(50) | | 生成方法 |
| generation_params | JSON | | 生成参数 |
| split_type | String(10) | | 数据集分割类型 |
| quality_score | Float | | 图像质量评分 |
| is_validated | Boolean | Default: False | 是否已验证 |
| dataset_id | Integer | FK(datasets.id), Not Null | 数据集ID |
| file_storage_id | Integer | FK(file_storages.id) | 文件存储ID |
| created_at | DateTime | Auto | 创建时间 |

**枚举值**:
- split_type: train, val, test

#### 2.3 annotations (标注表)
图像标注数据

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 标注ID |
| annotation_type | String(50) | Not Null | 标注类型 |
| class_label | String(100) | | 类别标签 |
| confidence | Float | | 置信度 |
| bbox_x | Float | | 边界框X坐标 |
| bbox_y | Float | | 边界框Y坐标 |
| bbox_width | Float | | 边界框宽度 |
| bbox_height | Float | | 边界框高度 |
| annotation_metadata | JSON | | 额外标注信息 |
| is_auto_generated | Boolean | Default: False | 是否自动生成 |
| is_verified | Boolean | Default: False | 是否已验证 |
| image_id | Integer | FK(images.id), Not Null | 图像ID |
| dataset_id | Integer | FK(datasets.id), Not Null | 数据集ID |
| annotator_id | Integer | FK(users.id) | 标注者ID |
| created_at | DateTime | Auto | 创建时间 |
| updated_at | DateTime | Auto | 更新时间 |

**枚举值**:
- annotation_type: classification, detection

#### 2.4 generation_tasks (生成任务表)
图像生成任务管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 任务ID |
| dataset_id | Integer | FK(datasets.id), Not Null | 数据集ID |
| status | String(50) | Default: pending | 任务状态 |
| progress | Float | Default: 0.0 | 进度百分比 |
| total_images | Integer | Not Null | 总图像数 |
| generated_images | Integer | Default: 0 | 已生成图像数 |
| failed_images | Integer | Default: 0 | 失败图像数 |
| generation_config | JSON | | 生成配置参数 |
| model_id | Integer | FK(ai_models.id) | 使用的AI模型ID |
| error_message | Text | | 错误信息 |
| creator_id | Integer | FK(users.id) | 创建者ID |
| created_at | DateTime | Auto | 创建时间 |
| started_at | DateTime | | 开始时间 |
| completed_at | DateTime | | 完成时间 |

**枚举值**:
- status: pending, running, completed, failed

### 3. AI模型管理模块

#### 3.1 ai_models (AI模型表)
AI模型信息和配置

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 模型ID |
| name | String(255) | Unique, Not Null | 模型名称 |
| description | Text | | 模型描述 |
| model_type | String(50) | Not Null | 模型类型 |
| status | String(50) | Default: ready | 模型状态 |
| version | String(50) | Default: 1.0.0 | 模型版本 |
| file_path | String(500) | | 模型文件路径 |
| file_size | Integer | | 文件大小(字节) |
| checksum | String(64) | | 文件校验和 |
| config | JSON | | 模型配置参数 |
| supported_targets | JSON | | 支持的目标类型 |
| supported_weather | JSON | | 支持的天气条件 |
| supported_terrain | JSON | | 支持的地形场景 |
| accuracy | Float | | 准确率 |
| inference_time | Float | | 推理时间(秒) |
| memory_usage | Integer | | 内存使用(MB) |
| training_dataset_size | Integer | | 训练数据集大小 |
| training_epochs | Integer | | 训练轮数 |
| training_loss | Float | | 训练损失 |
| usage_count | Integer | Default: 0 | 使用次数 |
| success_rate | Float | Default: 0.0 | 成功率 |
| creator_id | Integer | FK(users.id) | 创建者ID |
| created_at | DateTime | Auto | 创建时间 |
| updated_at | DateTime | Auto | 更新时间 |

**索引**:
- `ix_ai_models_name` (唯一)

**枚举值**:
- model_type: stable_diffusion, custom_diffusion, gan, vae, traditional
- status: training, ready, deprecated, error

#### 3.2 model_training_jobs (模型训练任务表)
模型训练任务管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 训练任务ID |
| model_id | Integer | FK(ai_models.id), Not Null | 模型ID |
| job_name | String(255) | Not Null | 任务名称 |
| status | String(50) | Default: pending | 任务状态 |
| progress | Float | Default: 0.0 | 进度百分比 |
| training_config | JSON | | 训练配置参数 |
| dataset_config | JSON | | 数据集配置 |
| final_loss | Float | | 最终损失 |
| final_accuracy | Float | | 最终准确率 |
| training_logs | Text | | 训练日志 |
| gpu_hours | Float | | GPU使用小时数 |
| memory_peak | Integer | | 内存峰值使用(MB) |
| error_message | Text | | 错误信息 |
| creator_id | Integer | FK(users.id) | 创建者ID |
| created_at | DateTime | Auto | 创建时间 |
| started_at | DateTime | | 开始时间 |
| completed_at | DateTime | | 完成时间 |

#### 3.3 model_evaluations (模型评估表)
模型评估结果

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 评估ID |
| model_id | Integer | FK(ai_models.id), Not Null | 模型ID |
| evaluation_name | String(255) | Not Null | 评估名称 |
| test_dataset_id | Integer | FK(datasets.id) | 测试数据集ID |
| accuracy | Float | | 准确率 |
| precision | Float | | 精确率 |
| recall | Float | | 召回率 |
| f1_score | Float | | F1分数 |
| fid_score | Float | | FID分数 |
| is_score | Float | | IS分数 |
| lpips_score | Float | | LPIPS分数 |
| evaluation_results | JSON | | 详细评估结果 |
| sample_images | JSON | | 样本图像路径 |
| evaluator_id | Integer | FK(users.id) | 评估者ID |
| created_at | DateTime | Auto | 创建时间 |

### 4. 系统管理模块

#### 4.1 system_configs (系统配置表)
系统配置参数管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 配置ID |
| config_key | String(100) | Unique, Not Null | 配置键 |
| config_type | String(50) | Not Null | 配置类型 |
| config_value | JSON | | 配置值 |
| default_value | JSON | | 默认值 |
| description | Text | | 配置描述 |
| is_active | Boolean | Default: True | 是否启用 |
| is_readonly | Boolean | Default: False | 是否只读 |
| validation_rules | JSON | | 验证规则 |
| updated_by | Integer | FK(users.id) | 更新者ID |
| created_at | DateTime | Auto | 创建时间 |
| updated_at | DateTime | Auto | 更新时间 |

**索引**:
- `ix_system_configs_config_key` (唯一)

**枚举值**:
- config_type: system, ai_model, generation, storage

#### 4.2 system_stats (系统统计表)
系统统计数据

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 统计ID |
| stat_date | DateTime | Not Null, Index | 统计日期 |
| total_users | Integer | Default: 0 | 总用户数 |
| active_users | Integer | Default: 0 | 活跃用户数 |
| new_users | Integer | Default: 0 | 新用户数 |
| total_datasets | Integer | Default: 0 | 总数据集数 |
| active_datasets | Integer | Default: 0 | 活跃数据集数 |
| new_datasets | Integer | Default: 0 | 新数据集数 |
| total_images | Integer | Default: 0 | 总图像数 |
| generated_images | Integer | Default: 0 | 生成图像数 |
| annotated_images | Integer | Default: 0 | 标注图像数 |
| total_models | Integer | Default: 0 | 总模型数 |
| active_models | Integer | Default: 0 | 活跃模型数 |
| training_jobs | Integer | Default: 0 | 训练任务数 |
| total_storage_used | Integer | Default: 0 | 总存储使用(字节) |
| image_storage_used | Integer | Default: 0 | 图像存储使用(字节) |
| model_storage_used | Integer | Default: 0 | 模型存储使用(字节) |
| avg_generation_time | Float | Default: 0.0 | 平均生成时间(秒) |
| avg_annotation_time | Float | Default: 0.0 | 平均标注时间(秒) |
| system_uptime | Float | Default: 0.0 | 系统运行时间(小时) |
| created_at | DateTime | Auto | 创建时间 |

**索引**:
- `ix_system_stats_stat_date`

#### 4.3 task_queues (任务队列表)
异步任务队列管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 任务ID |
| task_id | String(255) | Unique, Not Null | 任务唯一标识 |
| task_type | String(50) | Not Null | 任务类型 |
| status | String(50) | Default: pending | 任务状态 |
| priority | Integer | Default: 0 | 优先级 |
| task_data | JSON | | 任务参数 |
| result_data | JSON | | 任务结果 |
| worker_id | String(100) | | 工作节点ID |
| progress | Float | Default: 0.0 | 进度百分比 |
| retry_count | Integer | Default: 0 | 重试次数 |
| max_retries | Integer | Default: 3 | 最大重试次数 |
| error_message | Text | | 错误信息 |
| error_traceback | Text | | 错误堆栈 |
| user_id | Integer | FK(users.id) | 用户ID |
| created_at | DateTime | Auto | 创建时间 |
| started_at | DateTime | | 开始时间 |
| completed_at | DateTime | | 完成时间 |

**索引**:
- `ix_task_queues_task_id` (唯一)

#### 4.4 system_logs (系统日志表)
系统操作日志

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 日志ID |
| level | String(20) | Not Null, Index | 日志级别 |
| message | Text | Not Null | 日志消息 |
| module | String(100) | | 模块名 |
| function | String(100) | | 函数名 |
| request_id | String(100) | | 请求ID |
| user_id | Integer | FK(users.id) | 用户ID |
| ip_address | String(45) | | IP地址 |
| extra_data | JSON | | 额外数据 |
| created_at | DateTime | Auto | 创建时间 |

**索引**:
- `ix_system_logs_level`

#### 4.5 file_storages (文件存储表)
文件存储管理

| 字段名 | 类型 | 约束 | 描述 |
|--------|------|------|------|
| id | Integer | PK, Auto | 存储ID |
| file_hash | String(64) | Unique, Not Null | 文件哈希 |
| original_filename | String(255) | Not Null | 原始文件名 |
| file_path | String(500) | Not Null | 文件路径 |
| file_size | Integer | Not Null | 文件大小(字节) |
| mime_type | String(100) | | MIME类型 |
| storage_type | String(50) | Default: local | 存储类型 |
| storage_config | JSON | | 存储配置 |
| reference_count | Integer | Default: 0 | 引用计数 |
| access_count | Integer | Default: 0 | 访问次数 |
| uploaded_by | Integer | FK(users.id) | 上传者ID |
| created_at | DateTime | Auto | 创建时间 |
| last_accessed_at | DateTime | | 最后访问时间 |

**索引**:
- `ix_file_storages_file_hash` (唯一)

## 关系图

```
users (1) ----< (N) user_sessions
users (1) ----< (N) user_activities
users (1) ----< (N) datasets
users (1) ----< (N) ai_models
users (1) ----< (N) annotations
users (1) ----< (N) generation_tasks
users (1) ----< (N) model_training_jobs
users (1) ----< (N) model_evaluations
users (1) ----< (N) system_configs
users (1) ----< (N) task_queues
users (1) ----< (N) system_logs
users (1) ----< (N) file_storages

datasets (1) ----< (N) images
datasets (1) ----< (N) annotations
datasets (1) ----< (N) generation_tasks
datasets (1) ----< (N) model_evaluations

images (1) ----< (N) annotations
images (N) ----< (1) file_storages

ai_models (1) ----< (N) generation_tasks
ai_models (1) ----< (N) model_training_jobs
ai_models (1) ----< (N) model_evaluations
```

## 性能优化

### 索引策略
1. **主键索引**: 所有表的主键自动建立聚集索引
2. **唯一索引**: 用户名、邮箱、配置键等唯一字段
3. **外键索引**: 所有外键字段建立索引
4. **查询索引**: 常用查询字段如日志级别、统计日期等
5. **复合索引**: 根据查询模式建立复合索引

### 分区策略
- **日志表分区**: 按月分区system_logs表
- **统计表分区**: 按年分区system_stats表
- **活动表分区**: 按月分区user_activities表

### 缓存策略
- **Redis缓存**: 用户会话、系统配置、统计数据
- **查询缓存**: 频繁查询的数据集信息、模型信息
- **文件缓存**: 图像缩略图、模型元数据

## 数据备份与恢复

### 备份策略
1. **全量备份**: 每日凌晨进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **日志备份**: 实时备份事务日志
4. **文件备份**: 定期备份图像和模型文件

### 恢复策略
1. **时间点恢复**: 支持恢复到任意时间点
2. **表级恢复**: 支持单表数据恢复
3. **灾难恢复**: 异地备份和快速恢复机制

## 监控与维护

### 监控指标
- **连接数监控**: 数据库连接池使用情况
- **查询性能**: 慢查询监控和优化
- **存储空间**: 表空间和索引空间监控
- **锁等待**: 死锁和长时间锁等待监控

### 维护任务
- **统计信息更新**: 定期更新表统计信息
- **索引重建**: 定期重建碎片化索引
- **数据清理**: 清理过期日志和临时数据
- **性能调优**: 根据监控数据进行性能优化 