version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: picgen_postgres
    environment:
      POSTGRES_DB: picgen
      POSTGRES_USER: picgen_user
      POSTGRES_PASSWORD: picgen_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - picgen_network

  # Redis缓存
  redis:
    image: redis:6-alpine
    container_name: picgen_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - picgen_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: picgen_backend
    ports:
      - "3002:3002"
    environment:
      - DATABASE_URL=******************************************************/picgen
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - postgres
      - redis
    networks:
      - picgen_network
    restart: unless-stopped

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: picgen_frontend
    ports:
      - "3001:3001"
    environment:
      - REACT_APP_API_URL=http://localhost:3002
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - picgen_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: picgen_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - picgen_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  picgen_network:
    driver: bridge 