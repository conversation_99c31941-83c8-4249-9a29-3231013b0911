@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title Military Target Image Dataset Generation System - Windows Launcher

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║           Military Target Image Dataset Generation           ║
echo ║                    Windows Native Launcher                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Set color
color 0A

echo [1/6] Checking system environment...
echo.

:: Check Docker
echo Checking Docker Desktop...
docker --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Docker not installed or not running
    echo TIP: Please install and start Docker Desktop first
    echo Download: https://www.docker.com/products/docker-desktop
    goto :error
)

:: Check if Docker is running
docker info >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Docker Desktop not running, please start Docker Desktop first
    echo TIP: Make sure Docker Desktop is installed and running
    goto :error
)
echo SUCCESS: Docker is ready

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Node.js not installed
    echo TIP: Please install Node.js 16+ first
    echo Download: https://nodejs.org/
    goto :error
)
echo SUCCESS: Node.js is ready

:: Enhanced Python environment check
echo Checking Python environment...

:: Check if Python is installed
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Python not installed
    echo TIP: Please install Python 3.9+ first
    echo Download: https://www.python.org/downloads/
    echo NOTE: Check "Add Python to PATH" during installation
    goto :error
)

:: Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    Python version: !PYTHON_VERSION!

:: Check Python version requirement (3.9+)
python -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: Python version too low, need 3.9 or higher
    echo TIP: Current version: !PYTHON_VERSION!
    echo Download: https://www.python.org/downloads/
    goto :error
)

:: Check if pip is available
echo    Checking pip...
python -m pip --version >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: pip not installed or not available
    echo TIP: Trying to fix pip...
    python -m ensurepip --upgrade >nul 2>&1
    if !errorlevel! neq 0 (
        echo ERROR: pip fix failed
        echo TIP: Please manually install pip or reinstall Python
        goto :error
    )
    echo SUCCESS: pip fix successful
)

:: Check venv module
echo    Checking venv module...
python -c "import venv" >nul 2>&1
if !errorlevel! neq 0 (
    echo ERROR: venv module not available
    echo TIP: Please ensure Python installation is complete, or try reinstalling Python
    goto :error
)

:: Check critical Python packages
echo    Checking critical dependencies...
python -c "import ssl, urllib.request" >nul 2>&1
if !errorlevel! neq 0 (
    echo WARNING: SSL module may have issues, may affect package downloads
)

echo SUCCESS: Python environment is ready

echo.
echo [2/6] Creating project directories...
if not exist "data" mkdir data
if not exist "data\datasets" mkdir data\datasets
if not exist "data\uploads" mkdir data\uploads
if not exist "models" mkdir models
if not exist "logs" mkdir logs
if not exist "static" mkdir static
echo SUCCESS: Directory creation completed

echo.
echo [3/6] Starting database services...
docker-compose up -d postgres redis
if !errorlevel! neq 0 (
    echo ERROR: Database startup failed
    echo TIP: Please check if Docker Desktop is running properly
    goto :error
)
echo SUCCESS: Database services started

echo.
echo [4/6] Waiting for database initialization...
timeout /t 10 /nobreak >nul
echo SUCCESS: Database initialization completed

echo.
echo [5/6] Starting backend service...
cd backend

:: Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ERROR: requirements.txt file not found
    echo TIP: Please ensure you are running this script in the correct project directory
    cd ..
    goto :error
)

:: Create or check virtual environment
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
    if !errorlevel! neq 0 (
        echo ERROR: Virtual environment creation failed
        echo TIP: Please check Python installation and permissions
        cd ..
        goto :error
    )
    echo SUCCESS: Virtual environment created successfully
) else (
    echo Checking existing virtual environment...
    if not exist "venv\Scripts\python.exe" (
        echo WARNING: Virtual environment corrupted, recreating...
        rmdir /s /q venv
        python -m venv venv
        if !errorlevel! neq 0 (
            echo ERROR: Virtual environment rebuild failed
            cd ..
            goto :error
        )
    )
    echo SUCCESS: Virtual environment check completed
)

:: Activate virtual environment and install dependencies
echo Installing Python dependencies...
call venv\Scripts\activate.bat
if !errorlevel! neq 0 (
    echo ERROR: Virtual environment activation failed
    cd ..
    goto :error
)

:: Upgrade pip
python -m pip install --upgrade pip >nul 2>&1

:: Check and fix setuptools compatibility issues
echo Checking setuptools compatibility...
python -c "import setuptools; print('setuptools version:', setuptools.__version__)" >nul 2>&1
if !errorlevel! neq 0 (
    echo WARNING: setuptools may have issues, trying to fix...
    pip install "setuptools<70.0.0" >nul 2>&1
)

:: Check PyTorch compatibility and install dependencies
echo Checking PyTorch compatibility...

:: Try to install original requirements.txt
pip install -r requirements.txt >nul 2>&1
if !errorlevel! neq 0 (
    echo WARNING: Detected dependency compatibility issues, trying auto-fix...
    
    :: Step 1: Fix setuptools version
    echo Fixing setuptools version...
    pip install "setuptools<70.0.0" >nul 2>&1
    
    :: Step 2: Use compatible version requirements file
    if exist "requirements-compatible.txt" (
        echo Using compatible version configuration...
        pip install -r requirements-compatible.txt >nul 2>&1
        set INSTALL_RESULT=!errorlevel!
    ) else (
        echo Installing compatible PyTorch version...
        pip install "torch>=2.2.0" "torchvision>=0.17.0" --index-url https://download.pytorch.org/whl/cpu >nul 2>&1
        if !errorlevel! equ 0 (
            echo Installing other dependencies...
            pip install -r requirements.txt --no-deps >nul 2>&1
            set INSTALL_RESULT=!errorlevel!
        ) else (
            set INSTALL_RESULT=1
        )
    )
    
    :: Step 3: If still fails, try installing key packages individually
    if !INSTALL_RESULT! neq 0 (
        echo Trying to install critical dependencies individually...
        pip install fastapi uvicorn sqlalchemy alembic psycopg2-binary redis >nul 2>&1
        pip install diffusers transformers accelerate opencv-python Pillow numpy >nul 2>&1
        pip install pandas scikit-learn pydantic loguru requests >nul 2>&1
        set INSTALL_RESULT=!errorlevel!
    )
    
    if !INSTALL_RESULT! neq 0 (
        echo ERROR: Compatible version dependency installation also failed
        echo.
        echo Possible solutions:
        echo    1. Check network connection
        echo    2. Try using domestic mirror sources
        echo    3. Check Python version compatibility
        echo    4. Manually install PyTorch: pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
        echo    5. Try downgrading setuptools: pip install setuptools==68.0.0
        echo.
        cd ..
        goto :error
    )
    
    echo SUCCESS: Compatible version dependencies installed successfully
    echo TIP: Automatically fixed dependency compatibility issues
) else (
    echo SUCCESS: Original dependencies installed successfully
)

start "Backend Service - FastAPI" cmd /k "title Backend Service - FastAPI && color 0B && cd /d %CD% && venv\Scripts\activate.bat && echo Starting FastAPI server... && uvicorn main:app --reload --host 0.0.0.0 --port 3002"
cd ..
echo SUCCESS: Backend service started

echo.
echo [6/6] Starting frontend service...
cd frontend
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install >nul 2>&1
    if !errorlevel! neq 0 (
        echo ERROR: Frontend dependency installation failed
        echo TIP: Please check Node.js installation and network connection
        cd ..
        goto :error
    )
)
set PORT=3001
start "Frontend Service - React" cmd /k "title Frontend Service - React && color 0C && cd /d %CD% && echo Starting React development server... && npm start"
cd ..
echo SUCCESS: Frontend service started

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        Startup Complete!                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo Access URLs:
echo    Frontend App: http://localhost:3001
echo    Backend API: http://localhost:3002  
echo    API Docs: http://localhost:3002/docs
echo.
echo Database:
echo    PostgreSQL: localhost:5432
echo    Redis: localhost:6379
echo.
echo Usage Instructions:
echo    Frontend and backend run in separate windows
echo    Close corresponding windows to stop services
echo    Run stop-windows.bat to stop all services
echo.

:: Wait 3 seconds then auto-open browser
echo Opening browser in 3 seconds...
timeout /t 3 /nobreak >nul
start http://localhost:3001

echo.
echo Press any key to exit launcher...
pause >nul
exit

:error
echo.
echo ERROR: Startup failed! Please check the error messages above.
echo.
echo Common problem solutions:
echo    1. Ensure running as administrator
echo    2. Check firewall and antivirus settings
echo    3. Ensure all dependency software is properly installed
echo    4. Check network connection
echo    5. See detailed documentation: README-Windows.md
echo.
pause
exit /b 1 