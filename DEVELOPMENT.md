# 军事目标图像数据集生成系统 - 开发指南

## 项目概述

本系统是一个基于AI的军事目标图像数据集生成和管理平台，支持在不同天气和地形场景中生成自然、清晰、合理的模拟图像数据集。

## 开发环境搭建

### 环境要求

- **操作系统**: Linux/macOS/Windows
- **Python**: 3.9+
- **Node.js**: 16+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Windows额外要求**: Docker Desktop

### 快速启动

#### Linux/macOS 启动方式

1. **克隆项目**
```bash
git clone <repository-url>
cd pic_gen_tool
```

2. **使用启动脚本**
```bash
./start.sh
```

#### Windows 启动方式

**使用批处理脚本**
```cmd
# 双击运行或在命令提示符中执行
start-windows.bat
```

#### Windows启动脚本特性

- ✅ **环境检查**: 自动检查Docker、Node.js、Python环境
- ✅ **智能启动**: Windows原生环境优化
- ✅ **错误处理**: 完善的错误提示和故障排除建议
- ✅ **进程管理**: 在独立窗口中启动前后端服务
- ✅ **优雅停止**: 提供专用的停止脚本
- ✅ **Python环境智能检查**: 版本验证、pip修复、虚拟环境管理
- ✅ **自动修复功能**: 自动修复常见的Python环境问题
- ✅ **PyTorch兼容性检查**: 自动检测并安装兼容的PyTorch版本

#### 停止服务

**Windows环境停止服务**:
```cmd
# 批处理版本
stop-windows.bat
```

**Linux/macOS环境停止服务**:
```bash
# 按 Ctrl+C 停止服务，或使用
docker-compose down
```

3. **手动启动（可选）**
```bash
# 启动数据库
docker-compose up -d postgres redis

# 启动后端
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 3002

# 启动前端
cd frontend
npm install
PORT=3001 npm start
```

### 访问地址

- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:3002
- **API文档**: http://localhost:3002/docs
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

### Windows环境特别说明

1. **Docker Desktop配置**:
   - 分配足够的内存资源 (建议8GB+)
   - 启用文件共享
   - 确保虚拟化功能已开启

2. **常见问题解决**:
   - 端口占用：使用停止脚本清理端口
   - 权限问题：以管理员身份运行
   - 网络问题：检查防火墙和代理设置
   - **PyTorch兼容性**：自动检测并安装兼容版本

### PyTorch版本兼容性说明

项目原始配置使用PyTorch 2.1.0，但在某些环境下可能遇到兼容性问题：

#### 常见问题
- Python 3.12+与PyTorch 2.1.0不兼容
- 某些系统架构不支持特定PyTorch版本
- 网络环境导致的下载问题

#### 自动解决方案
启动脚本会自动：
1. 尝试安装原始版本依赖
2. 检测到兼容性问题时自动切换到兼容版本
3. 使用PyTorch 2.2.0+替代2.1.0
4. 保持功能完全兼容

#### 手动解决方案
```bash
# 使用兼容版本的requirements文件
cd backend
pip install -r requirements-compatible.txt

# 或直接安装兼容版本
pip install torch>=2.2.0 torchvision>=0.17.0
```

## 项目结构详解

```
picGen/
├── backend/                    # 后端服务
│   ├── app/                   # 应用核心代码
│   │   ├── api/              # API路由
│   │   │   └── v1/           # API版本1
│   │   │       ├── api.py    # 路由汇总
│   │   │       └── endpoints/ # 具体端点
│   │   │           ├── datasets.py    # 数据集管理
│   │   │           └── generation.py  # 图像生成
│   │   ├── core/             # 核心配置
│   │   │   ├── config.py     # 应用配置
│   │   │   ├── database.py   # 数据库连接
│   │   │   └── logging.py    # 日志配置
│   │   ├── models/           # 数据模型
│   │   │   └── dataset.py    # 数据集相关模型
│   │   ├── schemas/          # Pydantic模式
│   │   │   └── dataset.py    # 数据集模式
│   │   └── services/         # 业务逻辑
│   │       ├── dataset_service.py    # 数据集服务
│   │       └── generation_service.py # 生成服务
│   ├── main.py               # 应用入口
│   ├── requirements.txt      # Python依赖
│   └── Dockerfile           # Docker配置
├── frontend/                  # 前端应用
│   ├── src/                  # 源代码
│   │   ├── components/       # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── store/           # Redux状态管理
│   │   └── utils/           # 工具函数
│   ├── package.json         # Node.js依赖
│   └── Dockerfile          # Docker配置
├── data/                     # 数据目录
│   ├── datasets/            # 生成的数据集
│   └── uploads/             # 上传文件
├── models/                   # AI模型文件
├── logs/                     # 日志文件
├── docker-compose.yml        # Docker编排
├── start.sh                 # 启动脚本
└── README.md               # 项目文档
```

## 核心功能模块

### 1. 数据集管理 (Dataset Management)

**功能描述**: 提供数据集的完整生命周期管理

**主要特性**:
- ✅ 创建数据集 (Create)
- ✅ 查询数据集列表 (Read)
- ✅ 更新数据集信息 (Update)
- ✅ 删除数据集 (Delete)
- ✅ 数据集统计信息
- ✅ 自动分组 (训练集80%、验证集10%、测试集10%)

**API端点**:
- `GET /api/v1/datasets/` - 获取数据集列表
- `POST /api/v1/datasets/` - 创建数据集
- `GET /api/v1/datasets/{id}` - 获取数据集详情
- `PUT /api/v1/datasets/{id}` - 更新数据集
- `DELETE /api/v1/datasets/{id}` - 删除数据集
- `GET /api/v1/datasets/{id}/stats` - 获取统计信息

### 2. 图像生成 (Image Generation)

**功能描述**: 支持多种方式生成军事目标图像

**生成方法**:
1. **传统代码合成**: 基于规则的图像合成
2. **Stable Diffusion**: 基于扩散模型的生成
3. **定制模型**: 专门训练的军事目标模型

**支持的配置**:
- **军事目标**: 坦克、战机、舰艇、军用车辆、直升机、潜艇
- **天气条件**: 晴天、雨天、雪天、大雾、夜间、多云
- **地形场景**: 城市、岛屿、乡村、沙漠、森林、山地

**API端点**:
- `POST /api/v1/generation/start/{dataset_id}` - 开始生成
- `GET /api/v1/generation/status/{dataset_id}` - 获取生成状态
- `POST /api/v1/generation/stop/{dataset_id}` - 停止生成
- `POST /api/v1/generation/test` - 测试生成

### 3. 智能标注 (Annotation)

**功能描述**: 自动生成图像标注信息

**标注类型**:
- **分类标注**: 图像级别的类别标签
- **检测标注**: 目标边界框 (bbox) 标注

**标注信息**:
- 类别标签
- 置信度
- 边界框坐标 (相对坐标)
- 元数据信息

### 4. 数据可视化 (Visualization)

**功能描述**: 提供丰富的数据可视化功能

**可视化内容**:
- 数据集统计图表
- 生成进度监控
- 标注预览 (bbox可视化)
- 图像分布统计

## 开发规范

### 后端开发规范

1. **代码结构**
   - 遵循FastAPI最佳实践
   - 使用依赖注入模式
   - 分层架构: API -> Service -> Model

2. **数据库操作**
   - 使用SQLAlchemy ORM
   - 事务管理
   - 连接池配置

3. **API设计**
   - RESTful API设计
   - 统一的响应格式
   - 完整的错误处理

4. **日志记录**
   - 使用loguru进行日志管理
   - 分级日志记录
   - 日志轮转和压缩

### 前端开发规范

1. **组件设计**
   - 函数式组件 + Hooks
   - 组件复用和抽象
   - TypeScript类型安全

2. **状态管理**
   - Redux Toolkit
   - 异步状态管理
   - 缓存策略

3. **UI/UX设计**
   - Ant Design组件库
   - 响应式设计
   - 无障碍访问

## 部署指南

### 开发环境部署

使用Docker Compose进行本地开发:

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

### 生产环境部署

1. **环境变量配置**
```bash
# 创建.env文件
cp .env.example .env
# 编辑配置
vim .env
```

2. **数据库迁移**
```bash
cd backend
alembic upgrade head
```

3. **启动服务**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 测试指南

### 后端测试

```bash
cd backend
pytest tests/ -v
```

### 前端测试

```bash
cd frontend
npm test
```

### API测试

访问 http://localhost:3002/docs 进行交互式API测试

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接配置
   - 查看防火墙设置

2. **前端无法访问后端**
   - 检查CORS配置
   - 验证代理设置
   - 查看网络连接

3. **图像生成失败**
   - 检查模型文件
   - 验证GPU配置
   - 查看内存使用

### 日志查看

```bash
# 后端日志
tail -f logs/app.log

# Docker日志
docker-compose logs -f backend

# 前端日志
# 查看浏览器控制台
```

## 贡献指南

1. **Fork项目**
2. **创建功能分支**
3. **编写代码和测试**
4. **提交Pull Request**

### 代码提交规范

```bash
# 提交格式
git commit -m "feat: 添加数据集创建功能"
git commit -m "fix: 修复图像生成bug"
git commit -m "docs: 更新API文档"
```

## 技术栈详解

### 后端技术栈

- **FastAPI**: 现代、快速的Web框架
- **SQLAlchemy**: Python SQL工具包和ORM
- **Pydantic**: 数据验证和设置管理
- **PostgreSQL**: 关系型数据库
- **Redis**: 内存数据库和缓存
- **Loguru**: 现代化的日志库

### 前端技术栈

- **React 18**: 用户界面库
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: 企业级UI设计语言
- **Redux Toolkit**: 状态管理
- **Axios**: HTTP客户端

### AI/ML技术栈

- **PyTorch**: 深度学习框架
- **Diffusers**: 扩散模型库
- **Transformers**: 预训练模型库
- **OpenCV**: 计算机视觉库
- **PIL/Pillow**: 图像处理库

## 性能优化

### 后端优化

1. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池配置

2. **缓存策略**
   - Redis缓存
   - 应用级缓存
   - CDN配置

3. **异步处理**
   - 后台任务
   - 消息队列
   - 并发控制

### 前端优化

1. **代码分割**
   - 路由级分割
   - 组件懒加载
   - 动态导入

2. **资源优化**
   - 图片压缩
   - 静态资源CDN
   - 缓存策略

## 安全考虑

1. **API安全**
   - 身份验证
   - 权限控制
   - 输入验证

2. **数据安全**
   - 数据加密
   - 备份策略
   - 访问控制

3. **网络安全**
   - HTTPS配置
   - 防火墙设置
   - 安全头配置

## 监控和运维

1. **应用监控**
   - 性能指标
   - 错误追踪
   - 健康检查

2. **基础设施监控**
   - 服务器资源
   - 数据库性能
   - 网络状态

3. **日志管理**
   - 集中化日志
   - 日志分析
   - 告警机制 