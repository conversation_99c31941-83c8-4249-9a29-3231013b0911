# IDE 和编辑器
.cursor
.vscode/
.idea/
*.swp
*.swo
*~

# Python
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
log/

# 数据库
*.db
*.sqlite
*.sqlite3

# 模型文件（通常很大）
models/
*.pth
*.pt
*.ckpt
*.safetensors

# 数据文件
data/uploads/
data/datasets/
*.csv
*.json
*.pkl
*.pickle

# Node.js (如果前端使用)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 前端构建文件
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 缓存
.cache/
.pytest_cache/
.coverage
htmlcov/

# Docker
.dockerignore

# 其他
*.bak
*.orig
*.rej
.mypy_cache/
.dmypy.json
dmypy.json
