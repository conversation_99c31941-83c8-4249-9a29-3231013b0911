# Windows 环境启动指南

## 概述

本文档专门针对Windows用户，提供详细的安装和启动指南。系统完全支持Windows原生环境运行，无需WSL。

**重要更新**：为了避免中文编码问题，启动脚本已改为英文界面，但功能完全相同。

## 🚀 快速开始

### 一键启动

**双击运行启动脚本**
```
start-windows.bat
```

这是最简单的启动方式，具有：
- 🎨 清晰的英文界面设计（避免编码问题）
- 📊 清晰的进度显示
- 🚀 自动打开浏览器
- ✅ 完整的错误检查
- 🔧 **智能环境检查和修复**
- 🔄 **PyTorch版本兼容性自动修复**

脚本会自动：
- 验证系统依赖
- **检查Python版本和环境完整性**
- **自动修复pip问题**
- **处理PyTorch版本兼容性**
- 启动所有服务
- 打开浏览器访问应用

## 🔧 新增功能：智能Python环境检查

### 自动检查项目

所有启动脚本现在包含增强的Python环境检查：

1. **Python安装检查**
   - 检查Python是否已安装
   - 验证PATH环境变量配置

2. **版本兼容性检查**
   - 确保Python版本 ≥ 3.9
   - 显示当前Python版本信息

3. **pip可用性检查**
   - 检查pip是否正常工作
   - **自动修复pip问题**（使用`python -m ensurepip --upgrade`）

4. **虚拟环境模块检查**
   - 验证venv模块是否可用
   - 检查虚拟环境完整性

5. **SSL模块检查**
   - 检查SSL和urllib模块
   - 警告可能的网络下载问题

6. **虚拟环境智能管理**
   - 自动创建虚拟环境
   - 检测并修复损坏的虚拟环境
   - 自动升级pip到最新版本

### 自动修复功能

- **pip修复**：如果pip不可用，自动尝试使用`ensurepip`修复
- **虚拟环境重建**：检测到损坏的虚拟环境时自动重建
- **依赖安装错误处理**：提供详细的错误信息和解决建议

## 📋 环境要求

### 必需软件

| 软件 | 版本要求 | 下载地址 |
|------|----------|----------|
| Docker Desktop | 20.10+ | https://www.docker.com/products/docker-desktop |
| Node.js | 16+ | https://nodejs.org/ |
| **Python** | **3.9+** | https://www.python.org/downloads/ |
| Git | 最新版 | https://git-scm.com/download/win |

### ⚠️ Python安装重要提示

安装Python时**必须**勾选以下选项：
- ✅ **"Add Python to PATH"** - 将Python添加到系统PATH
- ✅ **"Install pip"** - 安装包管理器
- ✅ **"Install for all users"** - 为所有用户安装（推荐）

### 推荐软件

| 软件 | 用途 | 下载地址 |
|------|------|----------|
| Windows Terminal | 更好的终端体验 | Microsoft Store |
| Visual Studio Code | 代码编辑 | https://code.visualstudio.com/ |

## 🔧 详细安装步骤

### 步骤1：安装Docker Desktop

1. 下载并安装Docker Desktop
2. 启动Docker Desktop
3. 分配足够的资源：
   - 内存：8GB+
   - CPU：4核+
   - 磁盘：50GB+

### 步骤2：安装Node.js

1. 下载Node.js LTS版本
2. 运行安装程序，选择默认选项
3. 验证安装：
   ```cmd
   node --version
   npm --version
   ```

### 步骤3：安装Python（重要）

1. 下载Python 3.9+版本
2. **重要**：安装时勾选"Add Python to PATH"
3. **重要**：勾选"Install pip"
4. 验证安装：
   ```cmd
   python --version
   pip --version
   ```

**如果Python已安装但PATH配置有问题**：
- 手动添加Python安装目录到系统PATH
- 通常路径为：`C:\Users\<USER>\AppData\Local\Programs\Python\Python39\`
- 同时添加Scripts目录：`C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts\`

### 步骤4：克隆项目

```cmd
git clone <repository-url>
cd pic_gen_tool
```

## 🎯 启动方式详解

### 批处理脚本启动

**特点**：
- 简单易用，双击即可运行
- 英文界面，避免编码问题
- **智能Python环境检查和修复**
- **PyTorch版本兼容性自动处理**
- 适合所有用户

**使用方法**：
```cmd
# 方法1：双击文件
start-windows.bat

# 方法2：命令行运行
.\start-windows.bat
```

**界面示例**：
```
[1/6] Checking system environment...
Checking Docker Desktop...
SUCCESS: Docker is ready
Checking Node.js...
SUCCESS: Node.js is ready
Checking Python environment...
    Python version: 3.12.9
    Checking pip...
    Checking venv module...
    Checking critical dependencies...
SUCCESS: Python environment is ready
```

## 🛑 停止服务

### 使用停止脚本

```cmd
# 批处理版本
stop-windows.bat
```

### 手动停止

1. **关闭服务窗口**：关闭前端和后端的命令行窗口
2. **停止Docker容器**：
   ```cmd
   docker-compose down
   ```
3. **清理端口占用**：
   ```cmd
   netstat -ano | findstr :3001
   netstat -ano | findstr :3002
   ```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:3001 | 主要用户界面 |
| 后端API | http://localhost:3002 | API服务 |
| API文档 | http://localhost:3002/docs | Swagger文档 |
| 数据库 | localhost:5432 | PostgreSQL |
| 缓存 | localhost:6379 | Redis |

## 🔍 故障排除

### Python相关问题

#### 1. Python未找到
**错误信息**：`ERROR: Python not installed`
**解决方法**：
1. 确保Python已安装
2. 检查PATH环境变量
3. 重新安装Python并勾选"Add Python to PATH"

#### 2. Python版本过低
**错误信息**：`ERROR: Python version too low, need 3.9 or higher`
**解决方法**：
1. 下载并安装Python 3.9+
2. 卸载旧版本Python（可选）
3. 重新运行启动脚本

#### 3. pip不可用
**错误信息**：`ERROR: pip not installed or not available`
**自动修复**：脚本会自动尝试修复pip
**手动修复**：
```cmd
python -m ensurepip --upgrade
python -m pip install --upgrade pip
```

#### 4. PyTorch版本兼容性问题 🆕
**错误信息**：`Could not find a version that satisfies the requirement torch==2.1.0`
**自动修复**：启动脚本会自动检测并安装兼容版本
**手动解决方法**：
```cmd
# 方法1：安装兼容版本
pip install torch>=2.2.0 torchvision>=0.17.0 --index-url https://download.pytorch.org/whl/cpu

# 方法2：使用兼容的requirements文件
cd backend
pip install -r requirements-compatible.txt

# 方法3：GPU版本（如果有CUDA）
pip install torch>=2.2.0 torchvision>=0.17.0 --index-url https://download.pytorch.org/whl/cu118
```

**说明**：
- PyTorch 2.1.0可能与某些Python版本或系统架构不兼容
- 启动脚本会自动升级到兼容的PyTorch版本（2.2.0+）
- 功能完全兼容，不影响系统正常运行

#### 5. setuptools兼容性问题 🆕
**错误信息**：`AttributeError: module 'pkgutil' has no attribute 'ImpImporter'`
**自动修复**：启动脚本会自动降级setuptools版本
**手动解决方法**：
```cmd
# 方法1：使用专门的修复工具
cd backend
..\fix-python-env.bat

# 方法2：手动修复setuptools
pip uninstall setuptools -y
pip install "setuptools<70.0.0"

# 方法3：清理并重新安装
pip cache purge
pip install -r requirements-compatible.txt
```

**说明**：
- setuptools 70.0.0+与某些包构建工具不兼容
- 使用setuptools<70.0.0可以解决大部分构建问题
- 提供了专门的修复工具`fix-python-env.bat`

#### 6. 虚拟环境创建失败
**错误信息**：`ERROR: Virtual environment creation failed`
**解决方法**：
1. 确保有足够的磁盘空间
2. 检查文件夹权限
3. 以管理员身份运行脚本

#### 7. SSL模块问题
**警告信息**：`WARNING: SSL module may have issues`
**解决方法**：
1. 重新安装Python（完整安装）
2. 检查网络代理设置
3. 使用国内镜像源

### 界面编码问题 🆕

#### 问题描述
之前版本的脚本可能出现中文字符乱码问题，如：
```
'═══════════════════════════════════════════╝' is not recognized as an internal or external command
```

#### 解决方案
- **已修复**：最新版本脚本使用英文界面，完全避免编码问题
- **功能不变**：所有检查和修复功能保持完全相同
- **更好兼容性**：在所有Windows版本和终端环境下都能正常显示

### 常见问题

#### 1. Docker未启动
**错误信息**：`ERROR: Docker Desktop not running`
**解决方法**：
- 启动Docker Desktop
- 等待Docker完全启动（系统托盘图标不再转动）

#### 2. 端口被占用
**错误信息**：`端口3001/3002已被占用`
**解决方法**：
```cmd
# 查找占用进程
netstat -ano | findstr :3001
# 结束进程（替换PID）
taskkill /pid <PID> /f
```

#### 3. 依赖安装失败
**错误信息**：`ERROR: Frontend dependency installation failed`
**解决方法**：
1. 检查网络连接
2. 使用国内镜像源：
   ```cmd
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```
3. 清理pip缓存：
   ```cmd
   pip cache purge
   ```

#### 4. Node.js依赖安装失败
**错误信息**：`npm install失败`
**解决方法**：
```cmd
cd frontend
rmdir /s node_modules
del package-lock.json
npm cache clean --force
npm install
```

### 性能优化建议

1. **SSD存储**：将项目放在SSD上
2. **充足内存**：确保系统有足够的可用内存
3. **关闭杀毒软件实时扫描**：对项目目录添加排除规则
4. **Docker资源配置**：在Docker Desktop中分配足够的CPU和内存

### 网络问题

#### 代理环境
如果在公司网络或使用代理：

```cmd
# 设置npm代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# 设置pip代理
pip config set global.proxy http://proxy.company.com:8080
```

#### 防火墙设置
确保以下端口未被防火墙阻止：
- 3001 (前端)
- 3002 (后端)
- 5432 (PostgreSQL)
- 6379 (Redis)

## 📞 获取帮助

如果遇到问题，请按以下顺序尝试：

1. **查看错误日志**：注意启动脚本的错误输出
2. **检查系统要求**：确保所有依赖都已正确安装
3. **使用自动修复**：脚本会自动尝试修复常见问题
4. **重启服务**：使用停止脚本后重新启动
5. **清理环境**：删除虚拟环境和node_modules后重新安装
6. **查看文档**：参考DEVELOPMENT.md中的详细说明

## 📝 开发建议

### 推荐的开发环境设置

1. **安装Windows Terminal**
2. **配置VS Code**
3. **使用命令提示符或Windows Terminal**

### 项目目录建议

```
建议目录结构：
C:\Development\
├── pic_gen_tool\          # 项目根目录
├── tools\                 # 开发工具
└── workspace\             # 其他项目
```

### 环境变量设置

可以在系统环境变量中设置：
```
NODE_ENV=development
PYTHONPATH=C:\Development\pic_gen_tool\backend
```

## 🔧 Docker Desktop 配置

### 推荐设置

1. **资源分配**：
   - CPU：4核或更多
   - 内存：8GB或更多
   - 磁盘：50GB或更多

2. **网络设置**：
   - 使用默认网络配置
   - 确保端口转发正常工作

3. **文件共享**：
   - 添加项目目录到共享列表
   - 确保有足够的权限

### 故障排除

如果Docker启动失败：
1. 重启Docker Desktop
2. 检查Hyper-V是否启用
3. 确保虚拟化功能已开启
4. 检查Windows版本兼容性

---

**注意**：本指南基于Windows 10/11环境编写，专为Windows原生环境优化。最新版本启动脚本使用英文界面，避免了编码问题，同时包含智能Python环境检查和自动修复功能，大大提高了启动成功率。 