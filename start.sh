#!/bin/bash

# 军事目标图像数据集生成系统 - 启动脚本

echo "🚀 启动军事目标图像数据集生成系统..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data/datasets
mkdir -p data/uploads
mkdir -p models
mkdir -p logs
mkdir -p static

# 启动数据库服务
echo "🗄️ 启动数据库服务..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 检查后端依赖
if [ ! -f "backend/requirements.txt" ]; then
    echo "❌ 后端依赖文件不存在"
    exit 1
fi

# 启动后端服务（开发模式）
echo "🔧 启动后端服务..."
cd backend
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

# 启动后端
echo "🌐 启动FastAPI服务器..."
uvicorn main:app --reload --host 0.0.0.0 --port 3002 &
BACKEND_PID=$!

cd ..

# 启动前端服务（开发模式）
echo "⚛️ 启动前端服务..."
cd frontend

if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

echo "🌐 启动React开发服务器..."
PORT=3001 npm start &
FRONTEND_PID=$!

cd ..

echo "✅ 系统启动完成！"
echo ""
echo "🔗 访问地址："
echo "   前端应用: http://localhost:3001"
echo "   后端API: http://localhost:3002"
echo "   API文档: http://localhost:3002/docs"
echo ""
echo "📊 数据库连接："
echo "   PostgreSQL: localhost:5432"
echo "   Redis: localhost:6379"
echo ""
echo "⏹️ 停止服务请按 Ctrl+C"

# 等待用户中断
trap "echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; docker-compose down; exit" INT
wait 