import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

// 导入页面组件
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import DatasetPage from './pages/DatasetPage';
import GenerationPage from './pages/GenerationPage';
import ModelPage from './pages/ModelPage';
import SystemPage from './pages/SystemPage';
import ProfilePage from './pages/ProfilePage';
import ApiTestPage from './pages/ApiTestPage';

// 导入布局组件
import MainLayout from './components/Layout/MainLayout';

// 导入认证相关
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/Layout/ProtectedRoute';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <Routes>
            {/* 登录页面 */}
            <Route path="/login" element={<LoginPage />} />

            {/* 受保护的路由 */}
            <Route path="/" element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }>
              {/* 仪表盘 */}
              <Route index element={<DashboardPage />} />
              <Route path="dashboard" element={<DashboardPage />} />

              {/* 数据集管理 */}
              <Route path="datasets/*" element={<DatasetPage />} />

              {/* 图像生成 */}
              <Route path="generation/*" element={<GenerationPage />} />

              {/* 模型管理 */}
              <Route path="models/*" element={<ModelPage />} />

              {/* 系统管理 */}
              <Route path="system/*" element={<SystemPage />} />

              {/* 个人资料 */}
              <Route path="profile" element={<ProfilePage />} />

              {/* API测试 */}
              <Route path="api-test" element={<ApiTestPage />} />
            </Route>

            {/* 默认重定向 */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App;