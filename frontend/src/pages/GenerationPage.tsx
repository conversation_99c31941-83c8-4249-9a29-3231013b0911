import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Form,
  Select,
  InputNumber,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  Modal,
  Image,
  message,
  Tabs,
  Upload,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  UploadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { datasetService } from '../services/datasetService';
import { generationService } from '../services/generationService';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface GenerationConfig {
  dataset_id: number;
  total_images: number;
  targets: string[];
  weather_conditions: string[];
  terrain_scenes: string[];
  generation_method: string;
  image_size: {
    width: number;
    height: number;
  };
}

interface GenerationTask {
  id: number;
  dataset_id: number;
  total_images: number;
  status: string;
  created_at: string;
  updated_at: string;
  generation_params: any;
  error_message?: string;
}

const GenerationPage: React.FC = () => {
  const [form] = Form.useForm();
  const [datasets, setDatasets] = useState<any[]>([]);
  const [generationOptions, setGenerationOptions] = useState<any>({});
  const [tasks, setTasks] = useState<GenerationTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [activeTab, setActiveTab] = useState('batch');

  useEffect(() => {
    loadInitialData();
    loadTasks();
    // 设置定时刷新任务状态
    const interval = setInterval(loadTasks, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadInitialData = async () => {
    try {
      const [datasetsRes, optionsRes] = await Promise.all([
        datasetService.getDatasets(),
        generationService.getGenerationOptions()
      ]);
      setDatasets(datasetsRes);
      setGenerationOptions(optionsRes);
    } catch (error) {
      message.error('加载数据失败');
    }
  };

  const loadTasks = async () => {
    try {
      const tasksRes = await generationService.getGenerationTasks();
      setTasks(tasksRes.tasks || []);
    } catch (error) {
      console.error('加载任务失败:', error);
    }
  };

  const handleBatchGeneration = async (values: any) => {
    setLoading(true);
    try {
      const config: GenerationConfig = {
        dataset_id: values.dataset_id,
        total_images: values.total_images,
        targets: values.targets,
        weather_conditions: values.weather_conditions,
        terrain_scenes: values.terrain_scenes,
        generation_method: values.generation_method,
        image_size: {
          width: values.image_width,
          height: values.image_height
        }
      };

      await generationService.batchGeneration(config);
      message.success('批量生成任务已创建');
      form.resetFields();
      loadTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '创建生成任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSingleGeneration = async (values: any) => {
    setLoading(true);
    try {
      const config = {
        targets: [values.target],
        weather_conditions: [values.weather],
        terrain_scenes: [values.terrain],
        generation_method: values.generation_method,
        image_size: {
          width: values.image_width,
          height: values.image_height
        }
      };

      const result = await generationService.singleGeneration(config);
      message.success('单张图像生成成功');

      // 显示生成的图像
      if (result.image_url) {
        setPreviewImage(result.image_url);
        setPreviewVisible(true);
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '单张图像生成失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelTask = async (taskId: number) => {
    try {
      await generationService.cancelGenerationTask(taskId);
      message.success('任务已取消');
      loadTasks();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '取消任务失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'orange', text: '等待中' },
      running: { color: 'blue', text: '生成中' },
      completed: { color: 'green', text: '已完成' },
      failed: { color: 'red', text: '失败' },
      cancelled: { color: 'gray', text: '已取消' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const taskColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '数据集',
      dataIndex: 'dataset_id',
      key: 'dataset_id',
      render: (datasetId: number) => {
        const dataset = datasets.find(d => d.id === datasetId);
        return dataset ? dataset.name : `数据集 ${datasetId}`;
      }
    },
    {
      title: '图像数量',
      dataIndex: 'total_images',
      key: 'total_images'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: GenerationTask) => (
        <Space>
          {record.status === 'running' && (
            <Button
              size="small"
              icon={<PauseCircleOutlined />}
              onClick={() => handleCancelTask(record.id)}
            >
              取消
            </Button>
          )}
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              Modal.info({
                title: '任务详情',
                content: (
                  <div>
                    <p><strong>任务ID:</strong> {record.id}</p>
                    <p><strong>数据集ID:</strong> {record.dataset_id}</p>
                    <p><strong>图像数量:</strong> {record.total_images}</p>
                    <p><strong>状态:</strong> {getStatusTag(record.status)}</p>
                    <p><strong>生成参数:</strong></p>
                    <pre>{JSON.stringify(record.generation_params, null, 2)}</pre>
                    {record.error_message && (
                      <>
                        <p><strong>错误信息:</strong></p>
                        <Text type="danger">{record.error_message}</Text>
                      </>
                    )}
                  </div>
                ),
                width: 600
              });
            }}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>图像生成</Title>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="批量生成" key="batch">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="生成配置" extra={<SettingOutlined />}>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleBatchGeneration}
                  initialValues={{
                    generation_method: 'traditional',
                    image_width: 512,
                    image_height: 512,
                    total_images: 100
                  }}
                >
                  <Form.Item
                    name="dataset_id"
                    label="目标数据集"
                    rules={[{ required: true, message: '请选择数据集' }]}
                  >
                    <Select placeholder="选择数据集">
                      {datasets.map(dataset => (
                        <Option key={dataset.id} value={dataset.id}>
                          {dataset.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="total_images"
                    label="生成数量"
                    rules={[{ required: true, message: '请输入生成数量' }]}
                  >
                    <InputNumber
                      min={1}
                      max={10000}
                      style={{ width: '100%' }}
                      placeholder="1-10000张"
                    />
                  </Form.Item>

                  <Form.Item
                    name="targets"
                    label="军事目标"
                    rules={[{ required: true, message: '请选择军事目标' }]}
                  >
                    <Select mode="multiple" placeholder="选择军事目标类型">
                      {generationOptions.targets?.map((target: any) => (
                        <Option key={target.id} value={target.id}>
                          {target.name} - {target.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="weather_conditions"
                    label="天气条件"
                    rules={[{ required: true, message: '请选择天气条件' }]}
                  >
                    <Select mode="multiple" placeholder="选择天气条件">
                      {generationOptions.weather_conditions?.map((weather: any) => (
                        <Option key={weather.id} value={weather.id}>
                          {weather.name} - {weather.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="terrain_scenes"
                    label="地形场景"
                    rules={[{ required: true, message: '请选择地形场景' }]}
                  >
                    <Select mode="multiple" placeholder="选择地形场景">
                      {generationOptions.terrain_scenes?.map((terrain: any) => (
                        <Option key={terrain.id} value={terrain.id}>
                          {terrain.name} - {terrain.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="generation_method"
                    label="生成方法"
                    rules={[{ required: true, message: '请选择生成方法' }]}
                  >
                    <Select placeholder="选择生成方法">
                      {generationOptions.generation_methods?.map((method: any) => (
                        <Option key={method.id} value={method.id}>
                          {method.name} - {method.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="image_width"
                        label="图像宽度"
                        rules={[{ required: true, message: '请输入图像宽度' }]}
                      >
                        <Select placeholder="选择宽度">
                          {generationOptions.image_sizes?.map((size: any) => (
                            <Option key={size.width} value={size.width}>
                              {size.name || `${size.width}px`}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="image_height"
                        label="图像高度"
                        rules={[{ required: true, message: '请输入图像高度' }]}
                      >
                        <Select placeholder="选择高度">
                          {generationOptions.image_sizes?.map((size: any) => (
                            <Option key={size.height} value={size.height}>
                              {size.name || `${size.height}px`}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={<PlayCircleOutlined />}
                      size="large"
                      block
                    >
                      开始批量生成
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            <Col span={12}>
              <Card
                title="生成任务"
                extra={
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadTasks}
                    size="small"
                  >
                    刷新
                  </Button>
                }
              >
                <Table
                  columns={taskColumns}
                  dataSource={tasks}
                  rowKey="id"
                  size="small"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: false,
                    showQuickJumper: true
                  }}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="单张生成" key="single">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="单张图像生成">
                <Form
                  layout="vertical"
                  onFinish={handleSingleGeneration}
                  initialValues={{
                    generation_method: 'traditional',
                    image_width: 512,
                    image_height: 512
                  }}
                >
                  <Form.Item
                    name="target"
                    label="军事目标"
                    rules={[{ required: true, message: '请选择军事目标' }]}
                  >
                    <Select placeholder="选择军事目标类型">
                      {generationOptions.targets?.map((target: any) => (
                        <Option key={target.id} value={target.id}>
                          {target.name} - {target.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="weather"
                    label="天气条件"
                    rules={[{ required: true, message: '请选择天气条件' }]}
                  >
                    <Select placeholder="选择天气条件">
                      {generationOptions.weather_conditions?.map((weather: any) => (
                        <Option key={weather.id} value={weather.id}>
                          {weather.name} - {weather.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="terrain"
                    label="地形场景"
                    rules={[{ required: true, message: '请选择地形场景' }]}
                  >
                    <Select placeholder="选择地形场景">
                      {generationOptions.terrain_scenes?.map((terrain: any) => (
                        <Option key={terrain.id} value={terrain.id}>
                          {terrain.name} - {terrain.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="generation_method"
                    label="生成方法"
                    rules={[{ required: true, message: '请选择生成方法' }]}
                  >
                    <Select placeholder="选择生成方法">
                      {generationOptions.generation_methods?.map((method: any) => (
                        <Option key={method.id} value={method.id}>
                          {method.name} - {method.description}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="image_width"
                        label="图像宽度"
                        rules={[{ required: true, message: '请输入图像宽度' }]}
                      >
                        <Select placeholder="选择宽度">
                          {generationOptions.image_sizes?.map((size: any) => (
                            <Option key={size.width} value={size.width}>
                              {size.name || `${size.width}px`}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="image_height"
                        label="图像高度"
                        rules={[{ required: true, message: '请输入图像高度' }]}
                      >
                        <Select placeholder="选择高度">
                          {generationOptions.image_sizes?.map((size: any) => (
                            <Option key={size.height} value={size.height}>
                              {size.name || `${size.height}px`}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={<PlayCircleOutlined />}
                      size="large"
                      block
                    >
                      生成单张图像
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="生成预览">
                <div style={{ textAlign: 'center', minHeight: 300 }}>
                  {previewImage ? (
                    <Image
                      src={previewImage}
                      alt="生成的图像"
                      style={{ maxWidth: '100%', maxHeight: 300 }}
                    />
                  ) : (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: 300,
                      backgroundColor: '#f5f5f5',
                      border: '1px dashed #d9d9d9'
                    }}>
                      <Text type="secondary">生成的图像将在这里显示</Text>
                    </div>
                  )}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 图像预览模态框 */}
      <Modal
        visible={previewVisible}
        title="图像预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        <div style={{ textAlign: 'center' }}>
          <Image src={previewImage} alt="预览图像" style={{ maxWidth: '100%' }} />
        </div>
      </Modal>
    </div>
  );
};

export default GenerationPage;