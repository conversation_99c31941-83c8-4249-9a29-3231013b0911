import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Spin, Alert, Space, Tag } from 'antd';
import { UserOutlined, LockOutlined, RobotOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [testAccounts, setTestAccounts] = useState<Array<{ username: string; password: string; role: string; description: string }>>([]);
  const { login, isAuthenticated } = useAuth();

  useEffect(() => {
    // 获取测试账户信息
    const accounts = authService.getTestAccounts();
    setTestAccounts(accounts);
  }, []);

  // 如果已经登录，重定向到仪表盘
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleLogin = async (values: { username: string; password: string; remember?: boolean }) => {
    setLoading(true);
    try {
      await login(values.username, values.password);
      message.success('登录成功！');
    } catch (error: any) {
      console.error('Login error:', error);
      message.error(error.message || error.response?.data?.detail || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  // 快速登录功能
  const handleQuickLogin = (username: string, password: string) => {
    form.setFieldsValue({ username, password });
    handleLogin({ username, password });
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px',
        }}
      >
        {/* 头部 */}
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <RobotOutlined
            style={{
              fontSize: '48px',
              color: '#1890ff',
              marginBottom: 16,
            }}
          />
          <Title level={3} style={{ margin: 0 }}>
            军事目标图像数据集生成系统
          </Title>
          <Text type="secondary">请登录您的账户</Text>
        </div>

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名！' },
              { min: 3, message: '用户名至少3个字符！' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码！' },
              { min: 6, message: '密码至少6个字符！' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                width: '100%',
                height: '44px',
                borderRadius: '6px',
              }}
            >
              {loading ? <Spin size="small" /> : '登录'}
            </Button>
          </Form.Item>
        </Form>

        {/* 底部信息 */}
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            军事目标图像数据集生成与管理平台
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Version 1.0.0
          </Text>
        </div>

        {/* 测试账户信息 */}
        {testAccounts.length > 0 && (
          <div style={{ marginTop: 24 }}>
            <Alert
              message="开发环境 - 测试账户"
              description="点击下方账户可快速登录"
              type="info"
              icon={<InfoCircleOutlined />}
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {testAccounts.map((account, index) => (
                <Card
                  key={index}
                  size="small"
                  style={{
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                  }}
                  hoverable
                  onClick={() => handleQuickLogin(account.username, account.password)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>{account.username}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {account.description}
                      </Text>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <Tag color="blue">{account.role}</Tag>
                      <br />
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        密码: {account.password}
                      </Text>
                    </div>
                  </div>
                </Card>
              ))}
            </Space>
          </div>
        )}
      </Card>
    </div>
  );
};

export default LoginPage; 