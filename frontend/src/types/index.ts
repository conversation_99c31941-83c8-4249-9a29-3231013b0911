/**
 * 通用类型定义
 */

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  role: 'admin' | 'researcher' | 'user';
  is_active: boolean;
  avatar_url?: string;
  phone?: string;
  organization?: string;
  position?: string;
  bio?: string;
  datasets_count?: number;
  generated_images_count?: number;
  created_at: string;
  updated_at?: string;
  last_login_at?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  user: User;
}

// 数据集相关类型
export interface Dataset {
  id: number;
  name: string;
  description?: string;
  status: 'creating' | 'active' | 'completed' | 'archived' | 'error';
  total_images: number;
  generated_images: number;
  targets: string[];
  weather_conditions: string[];
  terrain_scenes: string[];
  generation_method: 'traditional' | 'stable_diffusion' | 'custom_model';
  image_size: { width: number; height: number };
  train_split: number;
  val_split: number;
  test_split: number;
  creator_id?: number;
  created_at: string;
  updated_at?: string;
}

export interface CreateDatasetRequest {
  name: string;
  description?: string;
  targets: string[];
  weather_conditions: string[];
  terrain_scenes: string[];
  generation_method?: string;
  image_size?: { width: number; height: number };
  train_split?: number;
  val_split?: number;
  test_split?: number;
}

// 图像相关类型
export interface Image {
  id: number;
  filename: string;
  file_path: string;
  file_size?: number;
  width?: number;
  height?: number;
  format?: string;
  target_type?: string;
  weather_condition?: string;
  terrain_scene?: string;
  generation_method?: string;
  generation_params?: any;
  split_type?: 'train' | 'val' | 'test';
  quality_score?: number;
  is_validated: boolean;
  dataset_id: number;
  created_at: string;
}

// 标注相关类型
export interface Annotation {
  id: number;
  annotation_type: 'classification' | 'detection';
  class_label?: string;
  confidence?: number;
  bbox_x?: number;
  bbox_y?: number;
  bbox_width?: number;
  bbox_height?: number;
  annotation_metadata?: any;
  is_auto_generated: boolean;
  is_verified: boolean;
  image_id: number;
  dataset_id: number;
  annotator_id?: number;
  created_at: string;
  updated_at?: string;
}

// 生成任务相关类型
export interface GenerationTask {
  id: number;
  dataset_id: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  total_images: number;
  generated_images: number;
  failed_images: number;
  generation_config?: any;
  model_id?: number;
  error_message?: string;
  creator_id?: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface CreateGenerationTaskRequest {
  dataset_id: number;
  total_images: number;
  generation_config?: any;
  model_id?: number;
}

// AI模型相关类型
export interface AIModel {
  id: number;
  name: string;
  description?: string;
  model_type: 'stable_diffusion' | 'custom_diffusion' | 'gan' | 'vae' | 'traditional';
  status: 'training' | 'ready' | 'deprecated' | 'error';
  version: string;
  file_path?: string;
  file_size?: number;
  config?: any;
  supported_targets?: string[];
  supported_weather?: string[];
  supported_terrain?: string[];
  accuracy?: number;
  inference_time?: number;
  memory_usage?: number;
  usage_count: number;
  success_rate: number;
  creator_id?: number;
  created_at: string;
  updated_at?: string;
}

// 系统统计类型
export interface SystemStats {
  id: number;
  stat_date: string;
  total_users: number;
  active_users: number;
  new_users: number;
  total_datasets: number;
  active_datasets: number;
  new_datasets: number;
  total_images: number;
  generated_images: number;
  annotated_images: number;
  total_models: number;
  active_models: number;
  training_jobs: number;
  total_storage_used: number;
  image_storage_used: number;
  model_storage_used: number;
  avg_generation_time: number;
  avg_annotation_time: number;
  system_uptime: number;
  created_at: string;
}

// 通用响应类型
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 配置选项类型
export interface ConfigOptions {
  targets: string[];
  weather_conditions: string[];
  terrain_scenes: string[];
  generation_methods: string[];
} 