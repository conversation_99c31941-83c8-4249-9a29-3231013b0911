.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.App-header {
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.1);
}

.App-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: bold;
}

.App-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.App-main {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.App-main h2 {
  margin-bottom: 30px;
  font-size: 2rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.feature-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  color: #fff;
}

.feature-card p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.api-links {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.api-links p {
  margin: 10px 0;
}

.api-links a {
  color: #ffd700;
  text-decoration: none;
  font-weight: bold;
}

.api-links a:hover {
  text-decoration: underline;
}

.ant-layout {
  min-height: 100vh;
}

.ant-layout-header {
  background: #001529;
  color: white;
}

.ant-layout-content {
  padding: 24px;
  background: #fff;
}

.ant-layout-sider {
  background: #001529;
} 