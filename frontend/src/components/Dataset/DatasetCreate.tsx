import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Typography,
  message,
  Steps,
  Space,
  InputNumber,
} from 'antd';
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import datasetService from '../../services/datasetService';
import { generationService } from '../../services/generationService';
import { CreateDatasetRequest } from '../../types';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const DatasetCreate: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [options, setOptions] = useState({
    targets: [] as string[],
    weather_conditions: [] as string[],
    terrain_scenes: [] as string[],
    generation_methods: [] as string[],
  });

  useEffect(() => {
    loadOptions();
  }, []);

  const loadOptions = async () => {
    try {
      const response = await generationService.getGenerationOptions();
      setOptions(response);
    } catch (error) {
      console.error('Failed to load options:', error);
      message.error('加载配置选项失败');
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const datasetData: CreateDatasetRequest = {
        name: values.name,
        description: values.description,
        targets: values.targets,
        weather_conditions: values.weather_conditions,
        terrain_scenes: values.terrain_scenes,
        generation_method: values.generation_method,
        image_size: {
          width: values.image_width || 512,
          height: values.image_height || 512,
        },
        train_split: values.train_split / 100,
        val_split: values.val_split / 100,
        test_split: values.test_split / 100,
      };

      const dataset = await datasetService.createDataset(datasetData);
      message.success('数据集创建成功！');
      navigate(`/datasets/${dataset.id}`);
    } catch (error) {
      console.error('Failed to create dataset:', error);
      message.error('创建数据集失败');
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    {
      title: '基本信息',
      content: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Form.Item
              label="数据集名称"
              name="name"
              rules={[
                { required: true, message: '请输入数据集名称' },
                { min: 3, message: '名称至少3个字符' },
              ]}
            >
              <Input placeholder="输入数据集名称" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="描述" name="description">
              <TextArea rows={4} placeholder="描述数据集的用途和特点..." />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '生成配置',
      content: (
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              label="军事目标类型"
              name="targets"
              rules={[{ required: true, message: '请选择至少一个目标类型' }]}
            >
              <Select
                mode="multiple"
                placeholder="选择目标类型"
                options={options.targets.map(target => ({ label: target, value: target }))}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="天气条件"
              name="weather_conditions"
              rules={[{ required: true, message: '请选择至少一个天气条件' }]}
            >
              <Select
                mode="multiple"
                placeholder="选择天气条件"
                options={options.weather_conditions.map(weather => ({ label: weather, value: weather }))}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="地形场景"
              name="terrain_scenes"
              rules={[{ required: true, message: '请选择至少一个地形场景' }]}
            >
              <Select
                mode="multiple"
                placeholder="选择地形场景"
                options={options.terrain_scenes.map(terrain => ({ label: terrain, value: terrain }))}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="生成方法"
              name="generation_method"
              rules={[{ required: true, message: '请选择生成方法' }]}
            >
              <Select placeholder="选择生成方法">
                <Option value="stable_diffusion">Stable Diffusion</Option>
                <Option value="traditional">传统方法</Option>
                <Option value="custom_model">自定义模型</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      ),
    },
    {
      title: '图像设置',
      content: (
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item label="图像宽度" name="image_width" initialValue={512}>
              <InputNumber min={256} max={2048} step={64} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item label="图像高度" name="image_height" initialValue={512}>
              <InputNumber min={256} max={2048} step={64} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} md={8}>
            <Form.Item label="训练集比例 (%)" name="train_split" initialValue={80}>
              <InputNumber min={50} max={90} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} md={8}>
            <Form.Item label="验证集比例 (%)" name="val_split" initialValue={10}>
              <InputNumber min={5} max={25} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} md={8}>
            <Form.Item label="测试集比例 (%)" name="test_split" initialValue={10}>
              <InputNumber min={5} max={25} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => navigate('/datasets/list')}>
              返回
            </Button>
            <Title level={2} style={{ margin: 0 }}>
              创建数据集
            </Title>
          </Space>
        </Col>
      </Row>

      <Card>
        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          {steps.map(item => (
            <Steps.Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            image_width: 512,
            image_height: 512,
            train_split: 80,
            val_split: 10,
            test_split: 10,
          }}
        >
          <div style={{ minHeight: 300 }}>
            {steps[currentStep].content}
          </div>

          <Row justify="space-between" style={{ marginTop: 32 }}>
            <Col>
              {currentStep > 0 && (
                <Button onClick={() => setCurrentStep(currentStep - 1)}>
                  上一步
                </Button>
              )}
            </Col>
            <Col>
              <Space>
                {currentStep < steps.length - 1 ? (
                  <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
                    下一步
                  </Button>
                ) : (
                  <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
                    创建数据集
                  </Button>
                )}
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default DatasetCreate;