/**
 * 数据集管理服务
 */

import apiClient from './api';
import { Dataset, CreateDatasetRequest, PaginatedResponse, Image, Annotation } from '../types';

class DatasetService {
  // 获取数据集列表
  async getDatasets(params?: {
    page?: number;
    size?: number;
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<Dataset>> {
    const response = await apiClient.get('/datasets', { params });
    return response.data;
  }

  // 获取数据集详情
  async getDataset(id: number): Promise<Dataset> {
    const response = await apiClient.get(`/datasets/${id}`);
    return response.data;
  }

  // 创建数据集
  async createDataset(data: CreateDatasetRequest): Promise<Dataset> {
    const response = await apiClient.post('/datasets', data);
    return response.data;
  }

  // 更新数据集
  async updateDataset(id: number, data: Partial<CreateDatasetRequest>): Promise<Dataset> {
    const response = await apiClient.put(`/datasets/${id}`, data);
    return response.data;
  }

  // 删除数据集
  async deleteDataset(id: number): Promise<void> {
    await apiClient.delete(`/datasets/${id}`);
  }

  // 获取数据集图像列表
  async getDatasetImages(datasetId: number, params?: {
    page?: number;
    size?: number;
    split_type?: string;
    target_type?: string;
  }): Promise<PaginatedResponse<Image>> {
    const response = await apiClient.get(`/datasets/${datasetId}/images`, { params });
    return response.data;
  }

  // 获取图像详情
  async getImage(imageId: number): Promise<Image> {
    const response = await apiClient.get(`/images/${imageId}`);
    return response.data;
  }

  // 上传图像
  async uploadImage(datasetId: number, file: File, metadata?: any): Promise<Image> {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    const response = await apiClient.post(`/datasets/${datasetId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 删除图像
  async deleteImage(imageId: number): Promise<void> {
    await apiClient.delete(`/images/${imageId}`);
  }

  // 获取图像标注
  async getImageAnnotations(imageId: number): Promise<Annotation[]> {
    const response = await apiClient.get(`/images/${imageId}/annotations`);
    return response.data;
  }

  // 创建标注
  async createAnnotation(data: Partial<Annotation>): Promise<Annotation> {
    const response = await apiClient.post('/annotations', data);
    return response.data;
  }

  // 更新标注
  async updateAnnotation(id: number, data: Partial<Annotation>): Promise<Annotation> {
    const response = await apiClient.put(`/annotations/${id}`, data);
    return response.data;
  }

  // 删除标注
  async deleteAnnotation(id: number): Promise<void> {
    await apiClient.delete(`/annotations/${id}`);
  }

  // 导出数据集
  async exportDataset(datasetId: number, format: string = 'coco'): Promise<Blob> {
    const response = await apiClient.get(`/datasets/${datasetId}/export`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  }

  // 获取数据集统计信息
  async getDatasetStats(datasetId: number): Promise<any> {
    const response = await apiClient.get(`/datasets/${datasetId}/stats`);
    return response.data;
  }

  // 验证数据集
  async validateDataset(datasetId: number): Promise<any> {
    const response = await apiClient.post(`/datasets/${datasetId}/validate`);
    return response.data;
  }

  // 获取配置选项
  async getConfigOptions(): Promise<{
    targets: string[];
    weather_conditions: string[];
    terrain_scenes: string[];
    generation_methods: string[];
  }> {
    const response = await apiClient.get('/datasets/config-options');
    return response.data;
  }
}

const datasetService = new DatasetService();
export default datasetService; 