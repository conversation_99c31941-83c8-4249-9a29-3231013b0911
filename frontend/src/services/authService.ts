/**
 * 用户认证服务
 */

import apiClient from './api';
import mockAuthService from './mockAuthService';
import { LoginRequest, LoginResponse, User } from '../types';

// 是否使用Mock服务（开发环境）
const USE_MOCK = process.env.NODE_ENV === 'development' && !process.env.REACT_APP_USE_REAL_API;

class AuthService {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    if (USE_MOCK) {
      return await mockAuthService.login(credentials);
    }

    const response = await apiClient.post('/auth/login', credentials);
    const data = response.data;

    // 保存token到localStorage
    if (data.access_token) {
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(data.user));
    }

    return data;
  }

  // 用户注册
  async register(userData: {
    username: string;
    email: string;
    password: string;
    full_name?: string;
  }): Promise<User> {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  }

  // 用户登出
  async logout(): Promise<void> {
    if (USE_MOCK) {
      return await mockAuthService.logout();
    }

    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
    }
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    if (USE_MOCK) {
      return await mockAuthService.getCurrentUser();
    }

    const response = await apiClient.get('/auth/me');
    return response.data;
  }

  // 更新用户信息
  async updateProfile(userData: Partial<User>): Promise<User> {
    if (USE_MOCK) {
      return await mockAuthService.updateProfile(userData);
    }

    const response = await apiClient.put('/auth/profile', userData);
    return response.data;
  }

  // 修改密码
  async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<void> {
    if (USE_MOCK) {
      return await mockAuthService.changePassword(data);
    }

    await apiClient.post('/auth/change-password', data);
  }

  // 上传头像
  async uploadAvatar(formData: FormData): Promise<{ avatar_url: string }> {
    if (USE_MOCK) {
      return await mockAuthService.uploadAvatar(formData);
    }

    const response = await apiClient.post('/auth/upload-avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    if (USE_MOCK) {
      return mockAuthService.isAuthenticated();
    }
    return !!localStorage.getItem('access_token');
  }

  // 获取本地存储的用户信息
  getLocalUser(): User | null {
    if (USE_MOCK) {
      return mockAuthService.getLocalUser();
    }
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  // 获取token
  getToken(): string | null {
    if (USE_MOCK) {
      return mockAuthService.getToken();
    }
    return localStorage.getItem('access_token');
  }

  // 获取测试账户信息（仅Mock模式）
  getTestAccounts(): Array<{ username: string; password: string; role: string; description: string }> {
    if (USE_MOCK) {
      return mockAuthService.getTestAccounts();
    }
    return [];
  }
}

const authService = new AuthService();
export default authService; 