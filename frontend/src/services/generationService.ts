/**
 * 图像生成服务
 */

import { apiClient } from './apiClient';

class GenerationService {
  // 获取生成任务列表
  async getGenerationTasks(params?: {
    skip?: number;
    limit?: number;
    status?: string;
    dataset_id?: number;
  }): Promise<{ tasks: any[]; total: number }> {
    const response = await apiClient.get('/generation/tasks', { params });
    return response.data;
  }

  // 获取生成任务详情
  async getGenerationTask(id: number): Promise<any> {
    const response = await apiClient.get(`/generation/tasks/${id}`);
    return response.data;
  }

  // 获取生成配置选项
  async getGenerationOptions(): Promise<any> {
    const response = await apiClient.get('/generation/config/options');
    return response.data;
  }

  // 取消生成任务
  async cancelGenerationTask(id: number): Promise<any> {
    const response = await apiClient.post(`/generation/tasks/${id}/cancel`);
    return response.data;
  }

  // 获取任务进度
  async getTaskProgress(id: number): Promise<{
    progress: number;
    status: string;
    generated_images: number;
    failed_images: number;
    current_step?: string;
  }> {
    const response = await apiClient.get(`/generation/tasks/${id}/progress`);
    return response.data;
  }

  // 批量生成图像
  async batchGeneration(config: any): Promise<any> {
    const response = await apiClient.post('/generation/batch', config);
    return response.data;
  }

  // 单张图像生成
  async singleGeneration(config: any): Promise<any> {
    const response = await apiClient.post('/generation/single', config);
    return response.data;
  }

  // 预览生成效果
  async previewGeneration(data: {
    target: string;
    weather_condition: string;
    terrain_scene: string;
    generation_method: string;
    model_id?: number;
  }): Promise<{ preview_url: string }> {
    const response = await apiClient.post('/generation/preview', data);
    return response.data;
  }

  // 获取生成历史
  async getGenerationHistory(params?: {
    page?: number;
    size?: number;
    user_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<GenerationTask>> {
    const response = await apiClient.get('/generation/history', { params });
    return response.data;
  }

  // 获取生成统计
  async getGenerationStats(params?: {
    start_date?: string;
    end_date?: string;
    group_by?: 'day' | 'week' | 'month';
  }): Promise<any> {
    const response = await apiClient.get('/generation/stats', { params });
    return response.data;
  }
}

export const generationService = new GenerationService();