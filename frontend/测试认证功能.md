# 认证功能测试指南

## 🎯 测试目标

验证军事目标图像数据集生成系统的完整认证功能，包括登录、登出、会话管理、路由保护等。

## 📋 测试清单

### ✅ 1. 预置用户账户测试

#### 测试步骤：
1. 访问 `http://localhost:3001`
2. 验证自动跳转到登录页面
3. 查看页面显示的测试账户信息

#### 预期结果：
- 显示三个测试账户卡片：
  - **admin** (系统管理员) - 密码: admin123
  - **user** (普通用户) - 密码: user123  
  - **researcher** (研究员) - 密码: research123
- 每个账户卡片显示角色和描述信息

### ✅ 2. 快速登录功能测试

#### 测试步骤：
1. 点击任意测试账户卡片
2. 观察表单自动填充
3. 验证自动登录过程

#### 预期结果：
- 用户名和密码自动填充到表单
- 显示登录加载状态
- 登录成功后跳转到仪表盘页面

### ✅ 3. 手动登录功能测试

#### 测试步骤：
1. 手动输入用户名: `admin`
2. 手动输入密码: `admin123`
3. 点击"登录"按钮

#### 预期结果：
- 表单验证通过
- 显示登录加载状态
- 登录成功提示
- 跳转到仪表盘页面

### ✅ 4. 登录错误处理测试

#### 测试步骤：
1. 输入错误的用户名: `wronguser`
2. 输入任意密码
3. 点击登录

#### 预期结果：
- 显示"用户名不存在"错误提示
- 不跳转页面
- 表单保持可编辑状态

#### 测试步骤2：
1. 输入正确用户名: `admin`
2. 输入错误密码: `wrongpass`
3. 点击登录

#### 预期结果：
- 显示"密码错误"错误提示
- 不跳转页面

### ✅ 5. 用户信息显示测试

#### 测试步骤：
1. 成功登录后查看页面头部
2. 检查用户头像和姓名显示
3. 点击用户头像下拉菜单

#### 预期结果：
- 显示用户头像（如果有）
- 显示用户姓名或用户名
- 下拉菜单包含：个人资料、退出登录选项

### ✅ 6. 路由保护测试

#### 测试步骤：
1. 在未登录状态下直接访问: `http://localhost:3001/dashboard`
2. 在未登录状态下访问: `http://localhost:3001/datasets`

#### 预期结果：
- 自动重定向到登录页面
- URL变为: `http://localhost:3001/login`

### ✅ 7. 会话保持测试

#### 测试步骤：
1. 登录成功后刷新页面
2. 关闭浏览器标签页重新打开
3. 清除浏览器缓存后访问

#### 预期结果：
- 刷新页面后保持登录状态
- 重新打开后仍然登录（如果选择了记住我）
- 清除缓存后需要重新登录

### ✅ 8. 登出功能测试

#### 测试步骤：
1. 在已登录状态下点击用户头像
2. 选择"退出登录"选项
3. 确认登出操作

#### 预期结果：
- 清除登录状态
- 跳转到登录页面
- 本地存储的token被清除

### ✅ 9. 页面导航测试

#### 测试步骤：
1. 登录后访问各个功能页面：
   - 仪表盘 (`/dashboard`)
   - 数据集管理 (`/datasets`)
   - 图像生成 (`/generation`)
   - 模型管理 (`/models`)
   - 系统管理 (`/system`)
   - API测试 (`/api-test`)

#### 预期结果：
- 所有页面正常访问
- 侧边栏菜单正确高亮
- 用户信息在所有页面正确显示

### ✅ 10. 个人资料管理测试

#### 测试步骤：
1. 点击用户头像选择"个人资料"
2. 查看个人信息显示
3. 测试编辑功能

#### 预期结果：
- 显示完整的用户信息
- 包含头像、姓名、邮箱等
- 显示使用统计数据

## 🔧 Mock服务验证

### 验证Mock服务正常工作：

1. **检查控制台日志**：
   - 打开浏览器开发者工具
   - 查看Console标签
   - 确认没有认证相关错误

2. **检查网络请求**：
   - 查看Network标签
   - 确认没有向后端API发送请求
   - 所有认证操作都在前端完成

3. **检查本地存储**：
   - 查看Application > Local Storage
   - 确认存储了access_token和user信息
   - Token格式为: `mock.{payload}.signature`

## 🐛 常见问题排查

### 问题1: 登录页面不显示测试账户
**解决方案**：
- 确认在开发环境运行
- 检查.env文件配置
- 重启开发服务器

### 问题2: 登录后立即跳转回登录页
**解决方案**：
- 检查浏览器控制台错误
- 清除浏览器缓存和localStorage
- 确认Mock服务正常工作

### 问题3: 页面访问被拒绝
**解决方案**：
- 确认已成功登录
- 检查token是否有效
- 重新登录

## 📊 测试结果记录

### 测试环境：
- 浏览器: Chrome/Firefox/Safari
- 操作系统: Windows/Mac/Linux
- Node.js版本: 
- React版本: 18.x

### 测试结果：
- [ ] 预置用户账户显示正常
- [ ] 快速登录功能正常
- [ ] 手动登录功能正常
- [ ] 错误处理正常
- [ ] 用户信息显示正常
- [ ] 路由保护正常
- [ ] 会话保持正常
- [ ] 登出功能正常
- [ ] 页面导航正常
- [ ] 个人资料管理正常

### 发现的问题：
1. 
2. 
3. 

### 修复建议：
1. 
2. 
3. 

## 🎉 测试完成

完成所有测试项目后，认证系统应该能够：
- ✅ 提供完整的用户认证流程
- ✅ 支持多个预置测试账户
- ✅ 实现安全的会话管理
- ✅ 保护所有需要认证的路由
- ✅ 提供良好的用户体验

系统现在已经准备好供用户使用和进一步开发！
