# 军事目标图像数据集生成系统 - 认证系统使用说明

## 🔐 认证系统概述

本系统提供了完整的用户认证功能，支持Mock模式和真实API模式，确保开发和生产环境的无缝切换。

## 🚀 快速开始

### 1. 启动应用

```bash
cd frontend
npm start
```

应用将在 `http://localhost:3001` 启动

### 2. 使用预置测试账户登录

系统提供了三个预置测试账户，可以直接使用：

#### 管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 系统管理员
- **权限**: 拥有所有权限，可以管理用户和系统设置

#### 普通用户账户
- **用户名**: `user`
- **密码**: `user123`
- **角色**: 普通用户
- **权限**: 可以创建数据集和生成图像

#### 研究员账户
- **用户名**: `researcher`
- **密码**: `research123`
- **角色**: 研究员
- **权限**: 专注于模型训练和研究工作

### 3. 快速登录

在登录页面，您可以：
- **手动输入**: 在表单中输入用户名和密码
- **快速登录**: 点击测试账户卡片即可自动填充并登录

## 🛠️ 认证系统特性

### Mock认证服务
- **自动启用**: 开发环境下默认使用Mock服务
- **真实体验**: 模拟真实的登录流程和响应
- **数据持久化**: 使用localStorage保存登录状态
- **Token管理**: 模拟JWT Token的生成和验证

### 用户会话管理
- **自动登录**: 刷新页面后保持登录状态
- **Token验证**: 自动验证Token有效性
- **会话过期**: 模拟Token过期处理
- **安全登出**: 清理所有本地存储数据

### 路由保护
- **受保护路由**: 未登录用户自动跳转到登录页
- **权限控制**: 基于用户角色的访问控制
- **自动重定向**: 登录成功后跳转到目标页面

## 🔧 配置说明

### 环境变量配置

在 `frontend/.env` 文件中：

```env
PORT=3001
REACT_APP_API_URL=http://localhost:3002
# 设置为true使用真实API，false或不设置使用Mock服务
# REACT_APP_USE_REAL_API=false
```

### Mock模式 vs 真实API模式

#### Mock模式（默认）
- **适用场景**: 开发环境、演示、测试
- **优势**: 无需后端服务，快速启动
- **数据**: 使用预置的测试数据
- **启用条件**: `NODE_ENV=development` 且未设置 `REACT_APP_USE_REAL_API=true`

#### 真实API模式
- **适用场景**: 生产环境、集成测试
- **要求**: 需要后端API服务运行在3002端口
- **启用方法**: 设置环境变量 `REACT_APP_USE_REAL_API=true`

## 📱 用户界面功能

### 登录页面
- **响应式设计**: 适配各种屏幕尺寸
- **测试账户展示**: 开发模式下显示可用的测试账户
- **快速登录**: 点击账户卡片快速登录
- **表单验证**: 用户名和密码的实时验证
- **加载状态**: 登录过程中的加载指示器
- **错误提示**: 友好的错误信息显示

### 用户信息管理
- **个人资料**: 查看和编辑个人信息
- **头像上传**: 支持头像图片上传
- **密码修改**: 安全的密码修改功能
- **使用统计**: 显示用户的使用数据

### 导航和菜单
- **用户头像**: 显示当前登录用户头像
- **用户菜单**: 个人资料、设置、登出等选项
- **权限菜单**: 根据用户角色显示相应菜单

## 🧪 测试功能

### 登录流程测试
1. 访问 `http://localhost:3001`
2. 如未登录，自动跳转到登录页面
3. 使用测试账户登录
4. 验证登录成功后跳转到仪表盘
5. 测试登出功能

### 会话管理测试
1. 登录后刷新页面，验证登录状态保持
2. 关闭浏览器重新打开，验证记住登录状态
3. 清除浏览器存储，验证自动跳转到登录页

### 权限控制测试
1. 未登录状态下访问受保护页面
2. 验证自动跳转到登录页面
3. 登录后验证可以正常访问所有功能

## 🔍 故障排除

### 常见问题

#### 1. 登录失败
- **检查用户名密码**: 确保使用正确的测试账户信息
- **查看控制台**: 检查浏览器控制台的错误信息
- **清除缓存**: 清除浏览器缓存和localStorage

#### 2. 页面无法访问
- **检查登录状态**: 确认已成功登录
- **验证Token**: 检查localStorage中的access_token
- **重新登录**: 尝试登出后重新登录

#### 3. Mock服务不工作
- **检查环境**: 确认在开发环境下运行
- **查看配置**: 检查.env文件配置
- **重启应用**: 重新启动开发服务器

### 调试信息

在浏览器控制台中可以查看：
- 认证状态信息
- API调用日志
- 错误详细信息
- Token验证结果

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查网络请求状态
3. 验证环境配置
4. 重启开发服务器

## 🔄 更新日志

### v1.0.0
- ✅ 完整的Mock认证系统
- ✅ 预置测试账户
- ✅ 快速登录功能
- ✅ 会话管理
- ✅ 路由保护
- ✅ 用户信息管理
- ✅ 响应式登录界面
