# 军事目标图像数据集生成系统 - 前端

## 项目概述

这是一个基于 React + TypeScript + Ant Design 的军事目标图像数据集生成和管理系统的前端应用。系统提供了完整的用户界面来管理数据集、生成图像、训练模型等功能。

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design 5** - UI组件库
- **React Router 6** - 路由管理
- **Axios** - HTTP客户端
- **Recharts** - 图表组件

## 功能特性

### 🔐 用户认证系统
- 用户登录/登出
- JWT Token 管理
- 受保护的路由
- 用户信息管理

### 📊 仪表盘
- 系统概览统计
- 数据集增长趋势图表
- 图像生成统计图表
- 快速操作入口

### 🗂️ 数据集管理
- **数据集列表**: 查看所有数据集，支持搜索、筛选、分页
- **创建数据集**: 配置目标类型、天气条件、地形场景等参数
- **数据集详情**: 查看数据集信息、图像列表、标注信息
- **数据集编辑**: 修改数据集基本信息
- **数据集删除**: 安全删除数据集
- **数据导出**: 导出数据集为标准格式

### 🎨 图像生成
- **生成任务管理**: 创建、查看、管理图像生成任务
- **批量生成**: 支持大规模图像批量生成
- **单张生成**: 快速生成单张图像
- **生成预览**: 预览生成效果
- **进度监控**: 实时查看生成进度
- **任务控制**: 暂停、恢复、取消生成任务

### 🤖 模型管理
- **模型列表**: 查看所有可用模型
- **模型训练**: 启动新的模型训练任务
- **模型评估**: 评估模型性能
- **模型部署**: 部署模型到生产环境

### ⚙️ 系统管理
- **用户管理**: 管理系统用户
- **系统配置**: 配置系统参数
- **系统日志**: 查看系统运行日志
- **系统监控**: 监控系统资源使用情况
- **存储管理**: 管理文件存储

### 👤 个人资料
- **基本信息**: 编辑个人基本信息
- **头像上传**: 上传和更换头像
- **安全设置**: 修改密码、两步验证
- **使用统计**: 查看个人使用统计

### 🔧 API测试工具
- **接口测试**: 测试所有API接口
- **实时响应**: 查看API响应结果
- **性能监控**: 监控API响应时间
- **自定义测试**: 支持自定义API测试

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── Dataset/       # 数据集相关组件
│   │   ├── Generation/    # 图像生成相关组件
│   │   ├── Layout/        # 布局组件
│   │   ├── Model/         # 模型相关组件
│   │   └── System/        # 系统管理组件
│   ├── contexts/          # React Context
│   │   └── AuthContext.tsx
│   ├── pages/             # 页面组件
│   │   ├── DashboardPage.tsx
│   │   ├── DatasetPage.tsx
│   │   ├── GenerationPage.tsx
│   │   ├── LoginPage.tsx
│   │   ├── ModelPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── SystemPage.tsx
│   │   └── ApiTestPage.tsx
│   ├── services/          # API服务
│   │   ├── api.ts         # API客户端配置
│   │   ├── authService.ts # 认证服务
│   │   ├── datasetService.ts # 数据集服务
│   │   ├── generationService.ts # 生成服务
│   │   └── modelService.ts # 模型服务
│   ├── types/             # TypeScript类型定义
│   │   └── index.ts
│   ├── App.tsx            # 主应用组件
│   └── index.tsx          # 应用入口
├── package.json
└── tsconfig.json
```

## API集成

系统完整集成了后端API，包括：

### 认证API
- `POST /auth/login` - 用户登录
- `GET /auth/me` - 获取当前用户信息
- `PUT /auth/profile` - 更新用户资料
- `POST /auth/upload-avatar` - 上传头像

### 数据集API
- `GET /datasets` - 获取数据集列表
- `POST /datasets` - 创建数据集
- `GET /datasets/{id}` - 获取数据集详情
- `PUT /datasets/{id}` - 更新数据集
- `DELETE /datasets/{id}` - 删除数据集
- `GET /datasets/{id}/images` - 获取数据集图像
- `GET /datasets/config-options` - 获取配置选项

### 图像生成API
- `GET /generation/tasks` - 获取生成任务列表
- `POST /generation/tasks` - 创建生成任务
- `GET /generation/tasks/{id}` - 获取任务详情
- `POST /generation/tasks/{id}/cancel` - 取消任务
- `GET /generation/tasks/{id}/progress` - 获取任务进度
- `POST /generation/batch` - 批量生成
- `POST /generation/single` - 单张生成
- `GET /generation/options` - 获取生成选项

### 模型API
- `GET /models` - 获取模型列表
- `POST /models` - 创建模型
- `GET /models/{id}` - 获取模型详情
- `POST /models/{id}/train` - 训练模型
- `POST /models/{id}/evaluate` - 评估模型

## 运行说明

### 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器 (端口3001)
npm start
```

### 生产环境构建

```bash
# 构建生产版本
npm run build
```

### 环境配置

创建 `.env` 文件：

```
PORT=3001
REACT_APP_API_URL=http://localhost:3002
```

## 主要特性

### 🎯 完整的用户体验
- 响应式设计，支持移动端
- 中文界面，符合国内用户习惯
- 直观的操作流程
- 实时状态反馈

### 🔒 安全性
- JWT Token认证
- 路由权限控制
- 安全的API调用
- 用户会话管理

### 📱 现代化界面
- Material Design风格
- 丰富的交互动画
- 一致的视觉体验
- 可访问性支持

### ⚡ 高性能
- 代码分割和懒加载
- 组件级别的状态管理
- 优化的API调用
- 缓存策略

## 开发规范

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 组件化开发
- 统一的命名规范

### 文件组织
- 按功能模块组织文件
- 可复用组件抽离
- 服务层统一管理
- 类型定义集中管理

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
