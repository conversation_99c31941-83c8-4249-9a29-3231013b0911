@echo off
chcp 65001 >nul
echo 🛑 停止军事目标图像数据集生成系统...
echo.

echo 📋 检查运行中的服务...

:: 停止Docker容器
echo 🗄️ 停止数据库服务...
docker-compose down

:: 查找并停止Node.js进程
echo ⚛️ 停止前端服务...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo table /nh 2^>nul') do (
    if not "%%i"=="INFO:" (
        echo   停止Node.js进程 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)

:: 查找并停止Python进程（uvicorn）
echo 🔧 停止后端服务...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul') do (
    if not "%%i"=="INFO:" (
        echo   停止Python进程 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)

:: 清理端口占用
echo 🧹 清理端口占用...
netstat -ano | findstr :3001 >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr :3001') do (
        echo   清理端口3001占用进程 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)

netstat -ano | findstr :3002 >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr :3002') do (
        echo   清理端口3002占用进程 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)

echo.
echo ✅ 系统已停止！
echo.
echo 💡 提示：
echo    - 所有服务已停止
echo    - Docker容器已关闭
echo    - 端口占用已清理
echo.
pause 